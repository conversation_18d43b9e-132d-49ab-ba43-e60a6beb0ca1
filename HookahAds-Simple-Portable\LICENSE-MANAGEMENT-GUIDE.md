# Banner Pro License Management Guide

This guide explains how to manage the licensing system for Banner Pro software.

## Overview

Banner Pro now includes a comprehensive licensing system that provides:
- **Hardware-bound licenses** - Each license works on only one computer
- **30-day free trial** - Users can try before buying
- **Secure activation** - License keys are validated and encrypted
- **Anti-tampering** - Hardware fingerprinting prevents license sharing

## License System Features

### 🔐 Security Features
- **Hardware Fingerprinting**: Licenses are bound to specific hardware
- **Encrypted Storage**: License data is encrypted and stored securely
- **Tamper Protection**: Moving licenses between computers is prevented
- **Checksum Validation**: License keys include validation checksums

### 📅 Trial System
- **30-day trial period** for new users
- **Hardware-bound trials** to prevent abuse
- **Automatic trial tracking** with days remaining display

### 🎯 License Key Format
- Format: `XXXX-XXXX-XXXX-XXXX` (e.g., `A1B2-C3D4-E5F6-G7H8`)
- Each key is unique and contains validation data
- Keys are case-insensitive but displayed in uppercase

## Generating License Keys

### Method 1: Interactive Generator
```bash
node license-generator.js interactive
```
This provides a user-friendly interface for generating licenses.

### Method 2: Command Line
```bash
# Generate single license
node license-generator.js generate --name "John Doe" --email "<EMAIL>" --save

# Generate batch licenses
node license-generator.js batch --count 50 --save --csv
```

### Method 3: Programmatic Generation
```javascript
const LicenseGenerator = require('./license-generator');
const generator = new LicenseGenerator();

const license = generator.generateSingleLicense({
  name: "Customer Name",
  email: "<EMAIL>",
  company: "Company Name"
});

console.log("License Key:", license.licenseKey);
```

## License Distribution Workflow

### 1. Generate License Keys
- Use the license generator to create unique keys
- Save customer information with each key
- Export as CSV for easy management

### 2. Distribute to Customers
- Send license key via email after purchase
- Include activation instructions
- Provide customer support contact

### 3. Customer Activation Process
1. Customer downloads and runs Banner Pro
2. License activation window appears
3. Customer enters license key and optional info
4. Software validates and activates license
5. License is bound to customer's hardware

## Customer Experience

### First Launch
1. **Trial Option**: Customer can start 30-day trial
2. **License Entry**: Customer can enter purchased license key
3. **Activation**: License is validated and activated

### Subsequent Launches
- Licensed users: Software starts normally
- Trial users: Reminder of days remaining
- Expired trial: Must enter license key

### License Activation Window
- Clean, professional interface
- Real-time license key formatting
- Hardware ID display for support
- Clear error messages and success feedback

## Technical Implementation

### Hardware Fingerprinting
The system creates a unique fingerprint based on:
- CPU model and count
- Total memory
- Network interface MAC addresses
- Operating system platform
- Computer hostname

### License Storage
- Location: `~/.bannerpro/license.dat`
- Format: Encrypted JSON data
- Contains: License key, hardware fingerprint, activation date

### Security Measures
- License data is encrypted using AES-256
- Hardware fingerprint prevents license sharing
- Tamper detection invalidates compromised licenses

## Managing Customer Support

### Common Issues and Solutions

**"License already activated on another machine"**
- Each license works on one computer only
- Customer needs to deactivate old installation or purchase additional license

**"Invalid license key format"**
- Ensure key follows XXXX-XXXX-XXXX-XXXX format
- Check for typos or missing characters

**"Hardware fingerprint mismatch"**
- Significant hardware changes can invalidate license
- May require manual license reset for legitimate cases

### Support Tools

**Check License Status:**
```javascript
const licenseSystem = new LicenseSystem();
console.log(licenseSystem.isLicenseActivated());
console.log(licenseSystem.getLicenseInfo());
```

**Reset License (for support):**
```javascript
licenseSystem.removeLicense(); // Use carefully!
```

## Business Considerations

### Pricing Strategy
- **Single License**: $29-49 (one computer)
- **Business License**: $79-99 (up to 5 computers)
- **Enterprise License**: $199+ (unlimited + support)

### License Types (Future Enhancement)
- Standard: Basic features
- Professional: Advanced features
- Enterprise: All features + priority support

### Revenue Protection
- Hardware binding prevents casual piracy
- Trial system encourages legitimate purchases
- Professional activation process builds trust

## Deployment Checklist

### Before Release
- [ ] Test license generation and activation
- [ ] Verify hardware fingerprinting works correctly
- [ ] Test trial period functionality
- [ ] Ensure license data is properly encrypted
- [ ] Test on different operating systems

### For Distribution
- [ ] Generate initial batch of license keys
- [ ] Set up customer database for license tracking
- [ ] Prepare customer support documentation
- [ ] Test complete purchase-to-activation workflow

### Customer Communication
- [ ] Create license key delivery email template
- [ ] Prepare activation instructions
- [ ] Set up support contact information
- [ ] Document common troubleshooting steps

## Security Best Practices

1. **Keep the secret key secure** - Store in environment variables in production
2. **Monitor license usage** - Track activations to detect abuse
3. **Regular updates** - Update fingerprinting algorithm if needed
4. **Backup license database** - Maintain records for customer support
5. **Secure key generation** - Generate keys on secure systems only

## Future Enhancements

- **Online activation** - Verify licenses against server database
- **License server** - Centralized license management
- **Automatic updates** - License-based feature updates
- **Usage analytics** - Track feature usage by license type
- **Floating licenses** - Network-based license sharing for enterprises
