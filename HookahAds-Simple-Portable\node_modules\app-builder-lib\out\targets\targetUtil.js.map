{"version": 3, "file": "targetUtil.js", "sourceRoot": "", "sources": ["../../src/targets/targetUtil.ts"], "names": [], "mappings": ";;;AAAA,6BAA4B;AAE5B,+CAA0C;AAE1C,kCAAiC;AAEjC,MAAa,QAAQ;IACnB,YAAqB,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;IAAG,CAAC;IAEpC,WAAW,CAAC,IAAY;QACtB,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;IACnC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,oBAAK,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,2CAA2C,KAAK,MAAM,EAAE,CAAC;YACzF,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,GAAG,CAAA;IACjB,CAAC;CACF;AAjBD,4BAiBC;AAEM,KAAK,UAAU,cAAc,CAAC,MAAc,EAAE,QAA+B,EAAE,IAAU;IAC9F,OAAO,IAAI,QAAQ,CAAC,MAAM,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAA;AACvE,CAAC;AAFD,wCAEC;AAEM,KAAK,UAAU,kBAAkB,CAAC,MAAc,EAAE,QAA+B,EAAE,IAAU;IAClG,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;IAC5E,MAAM,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;IACtD,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAC5C,OAAO,OAAO,CAAA;AAChB,CAAC;AALD,gDAKC;AAED,oEAAoE;AACpE,wGAAwG;AACxG,SAAgB,6BAA6B,CAAC,OAAgB,EAAE,qBAA8B;IAC5F,OAAO,qBAAqB,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAA;AACvI,CAAC;AAFD,sEAEC;AAED,oEAAoE;AACpE,SAAgB,oCAAoC,CAAC,OAAe;IAClE,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACrC,CAAC;AAFD,oFAEC", "sourcesContent": ["import * as path from \"path\"\nimport { Target, AppInfo } from \"../\"\nimport { Arch, debug } from \"builder-util\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport * as fs from \"fs/promises\"\n\nexport class StageDir {\n  constructor(readonly dir: string) {}\n\n  getTempFile(name: string) {\n    return this.dir + path.sep + name\n  }\n\n  cleanup() {\n    if (!debug.enabled || process.env.ELECTRON_BUILDER_REMOVE_STAGE_EVEN_IF_DEBUG === \"true\") {\n      return fs.rm(this.dir, { recursive: true, force: true })\n    }\n    return Promise.resolve()\n  }\n\n  toString() {\n    return this.dir\n  }\n}\n\nexport async function createStageDir(target: Target, packager: PlatformPackager<any>, arch: Arch): Promise<StageDir> {\n  return new StageDir(await createStageDirPath(target, packager, arch))\n}\n\nexport async function createStageDirPath(target: Target, packager: PlatformPackager<any>, arch: Arch): Promise<string> {\n  const tempDir = packager.info.stageDirPathCustomizer(target, packager, arch)\n  await fs.rm(tempDir, { recursive: true, force: true })\n  await fs.mkdir(tempDir, { recursive: true })\n  return tempDir\n}\n\n// https://github.com/electron-userland/electron-builder/issues/3100\n// https://github.com/electron-userland/electron-builder/commit/2539cfba20dc639128e75c5b786651b652bb4b78\nexport function getWindowsInstallationDirName(appInfo: AppInfo, isTryToUseProductName: boolean): string {\n  return isTryToUseProductName && /^[-_+0-9a-zA-Z .]+$/.test(appInfo.productFilename) ? appInfo.productFilename : appInfo.sanitizedName\n}\n\n// https://github.com/electron-userland/electron-builder/issues/6747\nexport function getWindowsInstallationAppPackageName(appName: string): string {\n  return appName.replace(/\\//g, \"\\\\\")\n}\n"]}