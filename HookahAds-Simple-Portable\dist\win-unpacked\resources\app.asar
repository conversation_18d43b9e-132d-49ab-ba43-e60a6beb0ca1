   
  
  
  {"files":{"EULA.txt":{"size":1795,"integrity":{"algorithm":"SHA256","hash":"c2adf3e724cf476044e58010ccbf86e061a2c816e02eeda94c4953f500ebb3c5","blockSize":4194304,"blocks":["c2adf3e724cf476044e58010ccbf86e061a2c816e02eeda94c4953f500ebb3c5"]},"offset":"0"},"HOW-TO-RUN.txt":{"size":3415,"integrity":{"algorithm":"SHA256","hash":"26c3079985734a7a847b83ba7e6e7d30ff873dedcf903dad00f08855fd2fae37","blockSize":4194304,"blocks":["26c3079985734a7a847b83ba7e6e7d30ff873dedcf903dad00f08855fd2fae37"]},"offset":"1795"},"LICENSE.txt":{"size":2189,"integrity":{"algorithm":"SHA256","hash":"d5b12ef9f85fc5b3c151778ae32b690616dc2427ea242effc5f6093b7084561b","blockSize":4194304,"blocks":["d5b12ef9f85fc5b3c151778ae32b690616dc2427ea242effc5f6093b7084561b"]},"offset":"5210"},"START-BANNER-PRO-HIDDEN.vbs":{"size":127,"integrity":{"algorithm":"SHA256","hash":"f9d8a23b71c82d2aff669047eb5564429d5f4c556080446914622d4e3a0586cb","blockSize":4194304,"blocks":["f9d8a23b71c82d2aff669047eb5564429d5f4c556080446914622d4e3a0586cb"]},"offset":"7399"},"START-BANNER-PRO-NO-TERMINAL.bat":{"size":54,"integrity":{"algorithm":"SHA256","hash":"554b6bbefc9adf3810fcf0b6ffded288871ebf8be62a44396d307a0fc5d50a3c","blockSize":4194304,"blocks":["554b6bbefc9adf3810fcf0b6ffded288871ebf8be62a44396d307a0fc5d50a3c"]},"offset":"7526"},"START-BANNER-PRO.bat":{"size":173,"integrity":{"algorithm":"SHA256","hash":"e3c6ae06f5f3e54904cdb2a3f830748d84eb791ff7edc8d7301b7815602a9e2a","blockSize":4194304,"blocks":["e3c6ae06f5f3e54904cdb2a3f830748d84eb791ff7edc8d7301b7815602a9e2a"]},"offset":"7580"},"banner.html":{"size":6265,"integrity":{"algorithm":"SHA256","hash":"27b2244fdff952b363c6fbbaa87b4bc9343b81c2aa4f9ce66ce34348f621d328","blockSize":4194304,"blocks":["27b2244fdff952b363c6fbbaa87b4bc9343b81c2aa4f9ce66ce34348f621d328"]},"offset":"7753"},"control.html":{"size":32398,"integrity":{"algorithm":"SHA256","hash":"feb970f65496b2a6713db5004d409d88db57da54309a75ee56af991d4a8bea01","blockSize":4194304,"blocks":["feb970f65496b2a6713db5004d409d88db57da54309a75ee56af991d4a8bea01"]},"offset":"14018"},"license-activation.html":{"size":9991,"integrity":{"algorithm":"SHA256","hash":"b400d5e2491c2b12eaf278cc6bab82f47e9f15401f2a7c569a769c193ae9c172","blockSize":4194304,"blocks":["b400d5e2491c2b12eaf278cc6bab82f47e9f15401f2a7c569a769c193ae9c172"]},"offset":"46416"},"license-system.js":{"size":7581,"integrity":{"algorithm":"SHA256","hash":"05478127be90c4d4740860c739a70275c7a52e212e101fcaf1811978b99fdc0d","blockSize":4194304,"blocks":["05478127be90c4d4740860c739a70275c7a52e212e101fcaf1811978b99fdc0d"]},"offset":"56407"},"main.js":{"size":7986,"integrity":{"algorithm":"SHA256","hash":"419b3760a042b2210ce441678e6bcb973b80fcf1a2e0a2839055914fa1f8653c","blockSize":4194304,"blocks":["419b3760a042b2210ce441678e6bcb973b80fcf1a2e0a2839055914fa1f8653c"]},"offset":"63988"},"package.json":{"size":409,"integrity":{"algorithm":"SHA256","hash":"55d09ccd5ae1e678ab66ddc50f4bfb5065b73e3cc51c1080aa5a4a1bce28ccfe","blockSize":4194304,"blocks":["55d09ccd5ae1e678ab66ddc50f4bfb5065b73e3cc51c1080aa5a4a1bce28ccfe"]},"offset":"71974"},"preload.js":{"size":1974,"integrity":{"algorithm":"SHA256","hash":"d793b03607b319ca088ca24c0703987c4cd6f4285bb41866ab4fc44da8f4ea09","blockSize":4194304,"blocks":["d793b03607b319ca088ca24c0703987c4cd6f4285bb41866ab4fc44da8f4ea09"]},"offset":"72383"}}}  END USER LICENSE AGREEMENT (EULA)
BANNER PRO SOFTWARE

PLEASE READ THIS END USER LICENSE AGREEMENT ("EULA") CAREFULLY BEFORE USING THE SOFTWARE. BY USING THE SOFTWARE, YOU AGREE TO BE BOUND BY THE TERMS OF THIS EULA.

1. DEFINITIONS
"Software" means the Banner Pro application and all related documentation.
"You" or "User" means the individual or entity using the Software.

2. LICENSE GRANT
Subject to the terms of this EULA, Banner Pro grants you a limited, non-exclusive, non-transferable license to use the Software for your internal business purposes.

3. RESTRICTIONS
You agree not to:
a) Modify, adapt, or create derivative works of the Software
b) Distribute, sell, rent, lease, or sublicense the Software
c) Use the Software for any illegal or unauthorized purpose
d) Attempt to reverse engineer or decompile the Software
e) Remove or modify any copyright or proprietary notices

4. INTELLECTUAL PROPERTY
The Software and all intellectual property rights therein are and shall remain the exclusive property of Banner Pro.

5. DATA AND PRIVACY
The Software may store configuration data locally on your device. No personal data is transmitted to Banner Pro servers.

6. UPDATES AND SUPPORT
Banner Pro may provide updates to the Software at its discretion. Support is provided on a best-effort basis.

7. DISCLAIMER OF WARRANTIES
THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED.

8. LIMITATION OF LIABILITY
BANNER PRO SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES.

9. TERMINATION
This EULA is effective until terminated. You may terminate it at any time by uninstalling the Software.

10. GOVERNING LAW
This EULA shall be governed by applicable local laws.

Contact: <EMAIL>
Version: 1.0
Date: 2024
BANNER PRO - PROFESSIONAL DIGITAL SIGNAGE

FEATURES: Text scrolls across the FULL SCREEN from right to left!
FEATURES: Multiple font size options (50% to 90% of banner height)!
FEATURES: Clean Exit - Smooth application experience!
 
REQUIREMENTS: 
- Node.js must be installed on the target computer 
- Download from: https://nodejs.org/ 
 
TO RUN THE APPLICATION:
1. Double-click "START-BANNER-PRO.bat"
 
FEATURES: 
- Banner window appears at bottom of screen 
- Control panel opens for managing ads 
- Add text, visual elements, AND pictures to your advertisements
- Customize colors, height, font size, and scroll speed
- Choose from 5 font sizes: Small, Medium-Small, Medium, Medium-Large, Large
- NEW: Slower scroll speeds available: 15s (Fast) to 120s (Very Slow)
- ALL ELEMENTS properly aligned regardless of banner height changes
- Pictures sized at 80% of font size for better alignment with text
- Text scrolls across the FULL SCREEN width from right to left
- NEW: Template System with 3 default templates:
  * Template 1: Store Hours & Info (2 sequences)
  * Template 2: Special Offers (3 sequences)
  * Template 3: VIP & Premium Services (2 sequences)
  * Load Template: Instantly fills all fields with saved settings
  * Save Template: Save your current configuration for later use
- No more filling fields each time - just click "Load Template" and go!
- Smooth continuous scrolling with proper looping
 
This version includes all source files and dependencies.
Just copy this folder to any computer with Node.js installed.

IMAGE TIPS:
- Use images from Desktop or Documents folder (avoid network drives)
- Keep file names simple (no special characters or spaces)
- Supported formats: JPG, PNG, GIF, BMP
- Keep images under 5MB each for best performance

QUICK START WITH TEMPLATES:
1. Choose your launcher:
   - "START-BANNER-PRO.bat" (shows terminal window)
   - "START-BANNER-PRO-NO-TERMINAL.bat" (minimized terminal)
   - "START-BANNER-PRO-HIDDEN.vbs" (completely hidden, no terminal)
2. Two windows open: Banner (bottom of screen) + Control Panel
3. EASY WAY: Click "📋 Load Template 1" (or 2, or 3) for instant setup
4. OR: Enter your own text, choose colors, adjust settings
5. Click "Apply Changes" to see your banner
6. Banner scrolls continuously across the bottom of your screen

LAUNCHER OPTIONS:
- START-BANNER-PRO.bat: Normal launch with terminal window visible
- START-BANNER-PRO-NO-TERMINAL.bat: Minimized terminal window
- START-BANNER-PRO-HIDDEN.vbs: Completely hidden launch (recommended)

TEMPLATE SYSTEM:
- 📋 Load Template: Instantly loads pre-configured advertisements
- 💾 Save Template: Save your current settings to file for future use
- Template 1: Store hours and general info (2 sequences)
- Template 2: Special offers and promotions (3 sequences)
- Template 3: VIP services and premium features (2 sequences)
- Templates save to: Browser localStorage (reliable)
- Simple and working storage system
- No more filling fields each time - just click and go!

MULTIPLE SEQUENCES SYSTEM:
- Up to 3 different advertisement sequences
- Each sequence: Text + Emoji + Custom Text + Picture
- Automatic cycling: Sequence1 → Sequence2 → Sequence3 → repeat
- Color-coded sections: Green (Seq1), Blue (Seq2), Orange (Seq3)
- Example: "Store Hours" → "Special Offers" → "VIP Services" → repeat
BANNER PRO SOFTWARE LICENSE AGREEMENT

Copyright (c) 2024 Banner Pro. All rights reserved.

IMPORTANT - READ CAREFULLY: This License Agreement ("Agreement") is a legal agreement between you (either an individual or a single entity) and Banner Pro for the Banner Pro software product, which includes computer software and may include associated media, printed materials, and "online" or electronic documentation ("Software").

BY INSTALLING, COPYING, OR OTHERWISE USING THE SOFTWARE, YOU AGREE TO BE BOUND BY THE TERMS OF THIS AGREEMENT. IF YOU DO NOT AGREE TO THE TERMS OF THIS AGREEMENT, DO NOT INSTALL OR USE THE SOFTWARE.

1. GRANT OF LICENSE
Banner Pro grants you a non-exclusive, non-transferable license to use the Software in accordance with the terms of this Agreement.

2. PERMITTED USES
You may:
- Install and use the Software on a single computer
- Make one backup copy of the Software for archival purposes
- Use the Software for commercial purposes in your business

3. RESTRICTIONS
You may not:
- Copy the Software except as expressly permitted in this Agreement
- Distribute, rent, lease, or sublicense the Software
- Reverse engineer, decompile, or disassemble the Software
- Remove or alter any copyright notices or other proprietary notices

4. OWNERSHIP
The Software is protected by copyright laws and international copyright treaties. Banner Pro retains all rights, title, and interest in and to the Software.

5. DISCLAIMER OF WARRANTIES
THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND. BANNER PRO DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.

6. LIMITATION OF LIABILITY
IN NO EVENT SHALL BANNER PRO BE LIABLE FOR ANY DAMAGES WHATSOEVER ARISING OUT OF THE USE OF OR INABILITY TO USE THE SOFTWARE.

7. TERMINATION
This Agreement is effective until terminated. Your rights under this Agreement will terminate automatically without notice if you fail to comply with any term of this Agreement.

8. GOVERNING LAW
This Agreement shall be governed by the laws of [Your Jurisdiction].

For questions about this license, contact: <EMAIL>
Set WshShell = CreateObject("WScript.Shell")
WshShell.Run chr(34) & "START-BANNER-PRO.bat" & Chr(34), 0
Set WshShell = Nothing
@echo off
cd /d "%~dp0"
start /min cmd /c "npm start"
@echo off
echo Starting Banner Pro...
echo.
echo Two windows will open:
echo 1. Banner window (at bottom of screen)
echo 2. Control panel (for managing ads)
echo.
npm start
<!DOCTYPE html>
<html>
<head>
  <title>Banner Display</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      background-color: transparent;
    }
    #banner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }
    #content {
      white-space: nowrap;
      position: absolute;
      left: 100%;
      animation: scroll 20s linear infinite;
      display: flex;
      align-items: center;
      height: 100%;
    }
    #content img {
      height: 80%;
      max-height: 150px;
      margin: 0 20px;
      vertical-align: middle;
      object-fit: contain;
    }
    @keyframes scroll {
      from { transform: translateX(0); }
      to { transform: translateX(calc(-100vw - 100%)); }
    }
  </style>
</head>
<body>
  <div id="banner">
    <div id="content"></div>
  </div>
  <script>
    // Use the secure API exposed through preload script
    window.electronAPI.onApplySettings((event, settings) => {
      try {
        const banner = document.getElementById('banner');
        const content = document.getElementById('content');

        // Apply banner height
        if (settings.height) {
          banner.style.height = settings.height + 'px';
        }

        // Apply colors
        if (settings.backgroundColor) {
          banner.style.backgroundColor = settings.backgroundColor;
        }
        if (settings.textColor) {
          content.style.color = settings.textColor;
        }

        // Apply font size (percentage of banner height) with stable alignment
        if (settings.fontSize && settings.height) {
          const fontSize = (settings.height * settings.fontSize / 100);
          content.style.fontSize = fontSize + 'px';
          content.style.lineHeight = settings.height + 'px';
          content.style.height = settings.height + 'px';
          content.style.display = 'flex';
          content.style.alignItems = 'center';

          // Store font size for other elements to use
          content.setAttribute('data-font-size', fontSize);
        }

        // Create multiple sequences: Sequence1 → Sequence2 → Sequence3 → Sequence1...
        content.innerHTML = '';

        console.log('Creating banner content with settings:', settings);

        // Function to create a single sequence
        function createSequence(sequence, index) {
          console.log(`Creating sequence ${index + 1}:`, sequence);

          // Add text
          if (sequence.text) {
            const textSpan = document.createElement('span');
            textSpan.textContent = sequence.text;
            textSpan.style.marginRight = '40px';
            textSpan.style.display = 'inline-block';
            content.appendChild(textSpan);
          }

          // Add visual element (emoji)
          if (sequence.visualElement) {
            const visualElement = document.createElement('span');
            visualElement.textContent = sequence.visualElement;
            visualElement.style.display = 'inline-block';
            visualElement.style.margin = '0 20px';
            visualElement.style.fontSize = 'inherit';
            visualElement.style.verticalAlign = 'middle';
            visualElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
            visualElement.style.fontWeight = 'bold';
            content.appendChild(visualElement);
          }

          // Add custom visual text
          if (sequence.customVisual) {
            const customElement = document.createElement('span');
            customElement.textContent = sequence.customVisual;
            customElement.style.display = 'inline-block';
            customElement.style.margin = '0 20px';
            customElement.style.fontSize = 'inherit';
            customElement.style.verticalAlign = 'middle';
            customElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
            customElement.style.fontWeight = 'bold';
            content.appendChild(customElement);
          }

          // Add picture
          if (sequence.picture) {
            const pictureContainer = document.createElement('div');
            pictureContainer.style.display = 'inline-block';
            pictureContainer.style.margin = '0 30px';
            pictureContainer.style.verticalAlign = 'middle';

            if (settings.fontSize && settings.height) {
              const fontSize = (settings.height * settings.fontSize / 100);
              const pictureSize = (fontSize * 0.8) + 'px';
              pictureContainer.style.width = pictureSize;
              pictureContainer.style.height = pictureSize;
            }
            pictureContainer.style.position = 'relative';
            pictureContainer.style.overflow = 'hidden';

            const imgElement = document.createElement('img');
            imgElement.src = sequence.picture;
            imgElement.style.width = '100%';
            imgElement.style.height = '100%';
            imgElement.style.display = 'block';
            imgElement.style.objectFit = 'contain';

            pictureContainer.appendChild(imgElement);
            content.appendChild(pictureContainer);
          }

          // Add spacing after sequence
          const spacer = document.createElement('span');
          spacer.style.marginRight = '80px';
          spacer.textContent = ' ';
          content.appendChild(spacer);
        }

        // Create all sequences
        if (settings.sequences && settings.sequences.length > 0) {
          // Create sequences multiple times for continuous scrolling
          for (let repeat = 0; repeat < 3; repeat++) {
            settings.sequences.forEach((sequence, index) => {
              createSequence(sequence, index);
            });
          }
        }











        // Update animation speed
        const speed = settings.scrollSpeed || 20;
        content.style.animation = `scroll ${speed}s linear infinite`;
      } catch (error) {
        console.error('Error applying settings:', error);
      }
    });
  </script>
</body>
</html><!DOCTYPE html>
<html>
<head>
  <title>Banner Pro Control Panel</title>
  <style>
    * {
      box-sizing: border-box;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .header {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 28px;
      font-weight: 300;
    }
    .header p {
      color: rgba(255, 255, 255, 0.8);
      margin: 5px 0 0 0;
      font-size: 14px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .control-group {
      background: rgba(255, 255, 255, 0.95);
      margin-bottom: 20px;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .control-group h3 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }
    label {
      display: block;
      margin-bottom: 8px;
      color: #555;
      font-weight: 500;
      font-size: 14px;
    }
    input, button, select, textarea {
      margin: 5px 0;
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }
    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      padding: 12px 24px;
      border: none;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    button:active {
      transform: translateY(0);
    }
    input[type="range"] {
      width: 100%;
      height: 6px;
      background: #e1e5e9;
      outline: none;
      border-radius: 3px;
      border: none;
    }
    input[type="range"]::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      background: #667eea;
      cursor: pointer;
      border-radius: 50%;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }
    .template-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 10px;
      margin-bottom: 15px;
    }
    .template-buttons button {
      padding: 10px 15px;
      font-size: 14px;
      border-radius: 6px;
    }
    .apply-button {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      font-size: 18px;
      padding: 15px 30px;
      margin-top: 20px;
      width: 100%;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Banner Pro</h1>
    <p>Professional Digital Signage Control Panel</p>
  </div>

  <div class="container">
    <div class="control-group">
      <h3>👁️ Banner Visibility</h3>
      <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
        <button id="toggleBannerBtn" onclick="toggleBanner()" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);">🔄 Toggle Banner</button>
        <button onclick="showBanner()" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);">👁️ Show Banner</button>
        <button onclick="hideBanner()" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">🙈 Hide Banner</button>
        <span id="bannerStatus" style="margin-left: 10px; font-weight: bold; color: #4CAF50;">Banner: Visible</span>
      </div>
      <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
        💡 Tip: Hide the banner when you need to click buttons at the bottom of your screen<br>
        ⌨️ Quick toggle: Press <strong>Ctrl+B</strong>
      </div>
    </div>

    <div class="control-group">
      <h3>📋 Templates</h3>
      <div class="template-buttons">
        <button onclick="loadTemplate(1)">📋 Load Template 1</button>
        <button onclick="loadTemplate(2)">📋 Load Template 2</button>
        <button onclick="loadTemplate(3)">📋 Load Template 3</button>
      </div>
      <div class="template-buttons">
        <button onclick="saveTemplate(1)">💾 Save Template 1</button>
        <button onclick="saveTemplate(2)">💾 Save Template 2</button>
        <button onclick="saveTemplate(3)">💾 Save Template 3</button>
      </div>
    <div id="templateStatus" style="margin-top: 5px; font-size: 12px; color: #666;">
      Templates: Save your current settings or load previously saved configurations
    </div>
  </div>

  <div class="control-group">
    <h3>⚙️ Sequence Configuration</h3>
    <label>Number of Sequences:</label>
    <select id="sequenceCount" onchange="updateSequenceInputs()" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="1">1 Sequence</option>
      <option value="2">2 Sequences</option>
      <option value="3">3 Sequences</option>
    </select>
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      Choose how many different advertisement sequences you want
    </div>
  </div>

  <!-- Sequence 1 -->
  <div class="sequence-section" id="sequence1" style="border: 2px solid #4CAF50; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9fff9;">
    <h3 style="margin-top: 0; color: #4CAF50;">📝 Sequence 1</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text1" placeholder="Enter text for sequence 1" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement1" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual1" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload1" accept="image/*">
      <button onclick="clearPicture(1)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview1" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <!-- Sequence 2 (hidden by default) -->
  <div class="sequence-section" id="sequence2" style="border: 2px solid #2196F3; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9f9ff; display: none;">
    <h3 style="margin-top: 0; color: #2196F3;">📝 Sequence 2</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text2" placeholder="Enter text for sequence 2" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement2" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual2" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload2" accept="image/*">
      <button onclick="clearPicture(2)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview2" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <!-- Sequence 3 (hidden by default) -->
  <div class="sequence-section" id="sequence3" style="border: 2px solid #FF9800; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #fffaf9; display: none;">
    <h3 style="margin-top: 0; color: #FF9800;">📝 Sequence 3</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text3" placeholder="Enter text for sequence 3" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement3" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual3" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload3" accept="image/*">
      <button onclick="clearPicture(3)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview3" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <div class="control-group">
    <label>Banner Height (2cm - 8cm):</label>
    <input type="range" id="height" min="75" max="300" value="75">
    <span id="heightValue">2cm</span>
  </div>

  <div class="control-group">
    <label>Background Color:</label>
    <input type="color" id="backgroundColor" value="#000000">
  </div>

  <div class="control-group">
    <label>Text Color:</label>
    <input type="color" id="textColor" value="#ffffff">
  </div>

  <div class="control-group">
    <label>Font Size:</label>
    <select id="fontSize" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="50">Small (50% of banner height)</option>
      <option value="60">Medium-Small (60% of banner height)</option>
      <option value="70" selected>Medium (70% of banner height)</option>
      <option value="80">Medium-Large (80% of banner height)</option>
      <option value="90">Large (90% of banner height)</option>
    </select>
  </div>

  <div class="control-group">
    <label>Scroll Speed (seconds per cycle):</label>
    <input type="range" id="scrollSpeed" min="15" max="120" value="30">
    <span id="speedValue">30s</span>
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      15s = Fast • 30s = Normal • 60s = Slow • 120s = Very Slow
    </div>
  </div>





    <button onclick="applySettings()" class="apply-button">🚀 Apply Changes</button>
  </div>

  <script>
    // Use the secure API exposed through preload script
    let selectedImages = [];
    let bannerVisible = true;

    // Height slider
    const heightSlider = document.getElementById('height');
    const heightValue = document.getElementById('heightValue');
    heightSlider.oninput = () => {
      const cm = (heightSlider.value / 37.5).toFixed(1);
      heightValue.textContent = cm + 'cm';
    };

    // Speed slider
    const speedSlider = document.getElementById('scrollSpeed');
    const speedValue = document.getElementById('speedValue');
    speedSlider.oninput = () => {
      speedValue.textContent = speedSlider.value + 's';
    };

    // Multiple sequences handling
    let sequences = [
      { text: '', visualElement: '', customVisual: '', picture: null },
      { text: '', visualElement: '', customVisual: '', picture: null },
      { text: '', visualElement: '', customVisual: '', picture: null }
    ];

    // Show/hide sequence inputs based on count
    function updateSequenceInputs() {
      const count = parseInt(document.getElementById('sequenceCount').value);

      for (let i = 1; i <= 3; i++) {
        const sequenceDiv = document.getElementById(`sequence${i}`);
        if (i <= count) {
          sequenceDiv.style.display = 'block';
        } else {
          sequenceDiv.style.display = 'none';
        }
      }
    }

    // Picture upload handling for multiple sequences
    function setupPictureUpload(sequenceNum) {
      document.getElementById(`pictureUpload${sequenceNum}`).onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(event) {
            sequences[sequenceNum - 1].picture = event.target.result;

            // Show preview
            const preview = document.getElementById(`picturePreview${sequenceNum}`);
            preview.innerHTML = `
              <img src="${sequences[sequenceNum - 1].picture}" style="max-width: 150px; max-height: 60px; border-radius: 4px;">
              <div style="margin-top: 5px; font-size: 10px; color: #4CAF50; font-weight: bold;">${file.name}</div>
            `;
          };
          reader.readAsDataURL(file);
        }
      };
    }

    // Clear picture function for sequences
    function clearPicture(sequenceNum) {
      sequences[sequenceNum - 1].picture = null;
      document.getElementById(`pictureUpload${sequenceNum}`).value = '';
      document.getElementById(`picturePreview${sequenceNum}`).innerHTML = 'No picture uploaded';
    }

    // Setup picture uploads for all sequences
    setupPictureUpload(1);
    setupPictureUpload(2);
    setupPictureUpload(3);

    // Template management
    let templates = {
      1: null,
      2: null,
      3: null
    };

    // Load templates from localStorage (simplified)
    function loadTemplatesFromStorage() {
      try {
        const savedTemplates = localStorage.getItem('bannerProTemplates');
        if (savedTemplates) {
          templates = JSON.parse(savedTemplates);
          console.log('Templates loaded from localStorage');
        } else {
          createDefaultTemplates();
        }
      } catch (error) {
        console.error('Error loading templates:', error);
        createDefaultTemplates();
      }
    }

    // Create default templates
    function createDefaultTemplates() {
      templates = {
        1: {
          name: "Store Hours & Info",
          sequenceCount: 2,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "Open Daily 12PM-2AM • Premium Service Experience",
              visualElement: "🌙",
              customVisual: "✦✧✦ OPEN LATE ✦✧✦",
              picture: null
            },
            {
              text: "Best Quality Tobacco • Relaxing Atmosphere",
              visualElement: "🍃",
              customVisual: "★★★ PREMIUM ★★★",
              picture: null
            }
          ]
        },
        2: {
          name: "Special Offers",
          sequenceCount: 3,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "Happy Hour 5-7PM • 50% Off All Items",
              visualElement: "🎉",
              customVisual: "★★★ SPECIAL OFFER ★★★",
              picture: null
            },
            {
              text: "Weekend Special • Live DJ Saturday",
              visualElement: "🎵",
              customVisual: "♪♫♪ LIVE MUSIC ♪♫♪",
              picture: null
            },
            {
              text: "Group Discounts Available • Call Now",
              visualElement: "👥",
              customVisual: "═══ GROUP DEALS ═══",
              picture: null
            }
          ]
        },
        3: {
          name: "VIP & Premium Services",
          sequenceCount: 2,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "VIP Lounge Available • Private Rooms",
              visualElement: "💎",
              customVisual: "◆◇◆ LUXURY EXPERIENCE ◆◇◆",
              picture: null
            },
            {
              text: "Premium Flavors • Exclusive Blends",
              visualElement: "🏆",
              customVisual: "▶ PREMIUM QUALITY ◀",
              picture: null
            }
          ]
        }
      };
      saveTemplatesToStorage();
    }

    // Save templates to localStorage (simplified)
    function saveTemplatesToStorage() {
      try {
        const templatesJson = JSON.stringify(templates);
        localStorage.setItem('bannerProTemplates', templatesJson);
        console.log('Templates saved to localStorage');
        return true;
      } catch (error) {
        console.error('Error saving templates:', error);
        return false;
      }
    }

    // Load a template
    function loadTemplate(templateNum) {
      const template = templates[templateNum];
      if (!template) {
        showTemplateStatus(`Template ${templateNum} not found`, 'error');
        return;
      }

      try {
        // Load basic settings
        document.getElementById('sequenceCount').value = template.sequenceCount;
        document.getElementById('height').value = template.height;
        document.getElementById('backgroundColor').value = template.backgroundColor;
        document.getElementById('textColor').value = template.textColor;
        document.getElementById('fontSize').value = template.fontSize;
        document.getElementById('scrollSpeed').value = template.scrollSpeed;

        // Update sequence inputs visibility
        updateSequenceInputs();

        // Load sequences
        template.sequences.forEach((seq, index) => {
          const seqNum = index + 1;
          if (seqNum <= template.sequenceCount) {
            document.getElementById(`text${seqNum}`).value = seq.text || '';
            document.getElementById(`visualElement${seqNum}`).value = seq.visualElement || '';
            document.getElementById(`customVisual${seqNum}`).value = seq.customVisual || '';

            // Clear picture preview
            document.getElementById(`picturePreview${seqNum}`).innerHTML = 'No picture uploaded';
            sequences[index].picture = seq.picture;
          }
        });

        showTemplateStatus(`Template ${templateNum} loaded: ${template.name}`, 'success');
      } catch (error) {
        showTemplateStatus(`Error loading template ${templateNum}`, 'error');
        console.error('Error loading template:', error);
      }
    }

    // Save current settings as template
    async function saveTemplate(templateNum) {
      try {
        console.log(`Attempting to save template ${templateNum}`);

        // Collect current settings
        const sequenceCount = parseInt(document.getElementById('sequenceCount').value);
        console.log('Sequence count:', sequenceCount);

        const currentSequences = [];

        for (let i = 1; i <= sequenceCount; i++) {
          const sequence = {
            text: document.getElementById(`text${i}`) ? document.getElementById(`text${i}`).value || '' : '',
            visualElement: document.getElementById(`visualElement${i}`) ? document.getElementById(`visualElement${i}`).value || '' : '',
            customVisual: document.getElementById(`customVisual${i}`) ? document.getElementById(`customVisual${i}`).value || '' : '',
            picture: sequences[i - 1] ? sequences[i - 1].picture : null
          };
          currentSequences.push(sequence);
          console.log(`Sequence ${i}:`, sequence);
        }

        // Create custom input dialog since prompt() is not supported in Electron
        const templateName = await showInputDialog(`Enter name for Template ${templateNum}:`, templates[templateNum]?.name || `Template ${templateNum}`);
        if (!templateName) {
          console.log('User cancelled template save');
          return; // User cancelled
        }

        const newTemplate = {
          name: templateName,
          sequenceCount: sequenceCount,
          height: parseInt(document.getElementById('height').value) || 100,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 45,
          sequences: currentSequences
        };

        console.log('New template:', newTemplate);

        templates[templateNum] = newTemplate;

        saveTemplatesToStorage();
        showTemplateStatus(`Template ${templateNum} saved: ${templateName}`, 'success');
        console.log(`Template ${templateNum} saved successfully`);
      } catch (error) {
        showTemplateStatus(`Error saving template ${templateNum}: ${error.message}`, 'error');
        console.error('Error saving template:', error);
      }
    }

    // Custom input dialog (since prompt() is not supported in Electron)
    function showInputDialog(message, defaultValue = '') {
      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      `;

      // Create dialog box
      const dialog = document.createElement('div');
      dialog.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        min-width: 300px;
        max-width: 500px;
      `;

      dialog.innerHTML = `
        <h3 style="margin-top: 0; color: #333;">${message}</h3>
        <input type="text" id="templateNameInput" value="${defaultValue}" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; margin: 10px 0;">
        <div style="text-align: right; margin-top: 15px;">
          <button id="cancelBtn" style="padding: 8px 15px; margin-right: 10px; background: #ccc; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
          <button id="okBtn" style="padding: 8px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Save</button>
        </div>
      `;

      overlay.appendChild(dialog);
      document.body.appendChild(overlay);

      // Focus on input
      const input = document.getElementById('templateNameInput');
      input.focus();
      input.select();

      // Return promise that resolves with the input value or null if cancelled
      return new Promise((resolve) => {
        const cleanup = () => {
          document.body.removeChild(overlay);
        };

        document.getElementById('okBtn').onclick = () => {
          const value = input.value.trim();
          cleanup();
          resolve(value || null);
        };

        document.getElementById('cancelBtn').onclick = () => {
          cleanup();
          resolve(null);
        };

        // Handle Enter key
        input.onkeydown = (e) => {
          if (e.key === 'Enter') {
            const value = input.value.trim();
            cleanup();
            resolve(value || null);
          } else if (e.key === 'Escape') {
            cleanup();
            resolve(null);
          }
        };

        // Handle clicking outside dialog
        overlay.onclick = (e) => {
          if (e.target === overlay) {
            cleanup();
            resolve(null);
          }
        };
      });
    }

    // Show template status message
    function showTemplateStatus(message, type) {
      const statusDiv = document.getElementById('templateStatus');
      statusDiv.textContent = message;

      if (type === 'success') {
        statusDiv.style.color = '#4CAF50';
      } else if (type === 'error') {
        statusDiv.style.color = '#f44336';
      } else if (type === 'warning') {
        statusDiv.style.color = '#FF9800';
      } else {
        statusDiv.style.color = '#666';
      }

      // Reset to default after 5 seconds (longer for error messages)
      setTimeout(() => {
        statusDiv.textContent = 'Templates: Save your current settings or load previously saved configurations';
        statusDiv.style.color = '#666';
      }, 5000);
    }

    // Initialize templates on page load
    loadTemplatesFromStorage();

    function applySettings() {
      try {
        // Collect data from active sequences
        const sequenceCount = parseInt(document.getElementById('sequenceCount').value);
        const activeSequences = [];

        for (let i = 1; i <= sequenceCount; i++) {
          const sequence = {
            text: document.getElementById(`text${i}`).value || '',
            visualElement: document.getElementById(`visualElement${i}`).value || '',
            customVisual: document.getElementById(`customVisual${i}`).value.trim() || '',
            picture: sequences[i - 1].picture
          };

          // Only add sequence if it has at least text
          if (sequence.text) {
            activeSequences.push(sequence);
          }
        }

        const settings = {
          height: parseInt(document.getElementById('height').value) || 75,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 20,
          sequences: activeSequences
        };

        window.electronAPI.updateSettings(settings);

        // Visual feedback
        const button = document.querySelector('button');
        const originalText = button.textContent;
        button.textContent = 'Applied!';
        button.style.backgroundColor = '#4CAF50';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 1000);
      } catch (error) {
        console.error('Error applying settings:', error);
        alert('Error applying settings. Please try again.');
      }
    }

    // Banner visibility functions
    function toggleBanner() {
      window.electronAPI.toggleBanner();
    }

    function showBanner() {
      window.electronAPI.showBanner();
    }

    function hideBanner() {
      window.electronAPI.hideBanner();
    }

    // Listen for banner visibility changes
    window.electronAPI.onBannerVisibilityChanged((event, isVisible) => {
      bannerVisible = isVisible;
      const statusElement = document.getElementById('bannerStatus');
      const toggleBtn = document.getElementById('toggleBannerBtn');

      if (isVisible) {
        statusElement.textContent = 'Banner: Visible';
        statusElement.style.color = '#4CAF50';
        toggleBtn.textContent = '🙈 Hide Banner';
      } else {
        statusElement.textContent = 'Banner: Hidden';
        statusElement.style.color = '#f44336';
        toggleBtn.textContent = '👁️ Show Banner';
      }
    });

    // Initialize banner status on load
    window.addEventListener('load', () => {
      // Banner starts visible by default
      const statusElement = document.getElementById('bannerStatus');
      const toggleBtn = document.getElementById('toggleBannerBtn');
      statusElement.textContent = 'Banner: Visible';
      statusElement.style.color = '#4CAF50';
      toggleBtn.textContent = '🙈 Hide Banner';
    });

    // Add keyboard shortcut (Ctrl+B) to toggle banner
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        toggleBanner();
      }
    });
  </script>
</body>
</html><!DOCTYPE html>
<html>
<head>
  <title>Banner Pro - License Activation</title>
  <style>
    * {
      box-sizing: border-box;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .activation-container {
      background: rgba(255, 255, 255, 0.95);
      padding: 40px;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-width: 500px;
      width: 90%;
      text-align: center;
    }
    .logo {
      font-size: 32px;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 10px;
    }
    .subtitle {
      color: #666;
      margin-bottom: 30px;
      font-size: 16px;
    }
    .license-input-group {
      margin-bottom: 20px;
    }
    .license-input {
      width: 100%;
      padding: 15px;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 18px;
      text-align: center;
      font-family: 'Courier New', monospace;
      letter-spacing: 2px;
      text-transform: uppercase;
      transition: all 0.3s ease;
    }
    .license-input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .license-input::placeholder {
      color: #999;
      letter-spacing: 1px;
    }
    .customer-info {
      margin-bottom: 20px;
      text-align: left;
    }
    .customer-info input {
      width: 100%;
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      margin-bottom: 10px;
      font-size: 14px;
    }
    .customer-info input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .button-group {
      margin-top: 30px;
    }
    .btn {
      padding: 15px 30px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin: 5px;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    .btn-secondary {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
      color: white;
    }
    .btn-secondary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(149, 165, 166, 0.3);
    }
    .trial-info {
      background: rgba(255, 193, 7, 0.1);
      border: 2px solid #ffc107;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
      color: #856404;
    }
    .trial-info.expired {
      background: rgba(220, 53, 69, 0.1);
      border-color: #dc3545;
      color: #721c24;
    }
    .status-message {
      margin-top: 20px;
      padding: 15px;
      border-radius: 10px;
      font-weight: 500;
    }
    .status-success {
      background: rgba(40, 167, 69, 0.1);
      border: 2px solid #28a745;
      color: #155724;
    }
    .status-error {
      background: rgba(220, 53, 69, 0.1);
      border: 2px solid #dc3545;
      color: #721c24;
    }
    .license-format {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    .hardware-info {
      font-size: 11px;
      color: #999;
      margin-top: 20px;
      padding: 10px;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 5px;
    }
  </style>
</head>
<body>
  <div class="activation-container">
    <div class="logo">🚀 Banner Pro</div>
    <div class="subtitle">Professional Digital Signage Solution</div>
    
    <div id="trialInfo" class="trial-info" style="display: none;">
      <strong>Trial Version</strong><br>
      <span id="trialStatus"></span>
    </div>

    <div class="license-input-group">
      <input type="text" id="licenseKey" class="license-input" placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19">
      <div class="license-format">Enter your license key in the format: XXXX-XXXX-XXXX-XXXX</div>
    </div>

    <div class="customer-info">
      <input type="text" id="customerName" placeholder="Your Name (Optional)">
      <input type="email" id="customerEmail" placeholder="Your Email (Optional)">
      <input type="text" id="customerCompany" placeholder="Company Name (Optional)">
    </div>

    <div class="button-group">
      <button class="btn btn-primary" onclick="activateLicense()">🔑 Activate License</button>
      <button class="btn btn-secondary" onclick="startTrial()" id="trialBtn">⏱️ Start Trial</button>
      <button class="btn btn-secondary" onclick="continueTrial()" id="continueBtn" style="display: none;">▶️ Continue</button>
    </div>

    <div id="statusMessage" class="status-message" style="display: none;"></div>

    <div class="hardware-info">
      <strong>Hardware ID:</strong> <span id="hardwareId">Loading...</span><br>
      <small>This license will be bound to this specific computer</small>
    </div>
  </div>

  <script>
    let hardwareFingerprint = '';

    // Format license key input
    document.getElementById('licenseKey').addEventListener('input', function(e) {
      let value = e.target.value.replace(/[^A-Z0-9]/g, '').toUpperCase();
      let formatted = value.match(/.{1,4}/g)?.join('-') || value;
      if (formatted.length > 19) formatted = formatted.substring(0, 19);
      e.target.value = formatted;
    });

    // Initialize on load
    window.addEventListener('load', async () => {
      await checkLicenseStatus();
      await loadTrialStatus();
      await getHardwareFingerprint();
    });

    async function checkLicenseStatus() {
      try {
        const result = await window.electronAPI.checkLicense();
        if (result.isActivated) {
          showStatus('License already activated! Starting application...', 'success');
          setTimeout(() => {
            window.electronAPI.startMainApp();
          }, 2000);
        }
      } catch (error) {
        console.error('Error checking license:', error);
      }
    }

    async function loadTrialStatus() {
      try {
        const trialStatus = await window.electronAPI.getTrialStatus();
        const trialInfo = document.getElementById('trialInfo');
        const trialStatusSpan = document.getElementById('trialStatus');
        const trialBtn = document.getElementById('trialBtn');
        const continueBtn = document.getElementById('continueBtn');

        if (trialStatus.daysRemaining > 0) {
          trialInfo.style.display = 'block';
          trialStatusSpan.textContent = `${trialStatus.daysRemaining} days remaining`;
          trialBtn.style.display = 'none';
          continueBtn.style.display = 'inline-block';
        } else if (trialStatus.daysRemaining === 0) {
          trialInfo.style.display = 'block';
          trialInfo.classList.add('expired');
          trialStatusSpan.textContent = 'Trial expired - License required';
          trialBtn.style.display = 'none';
          continueBtn.style.display = 'none';
        }
      } catch (error) {
        console.error('Error loading trial status:', error);
      }
    }

    async function getHardwareFingerprint() {
      try {
        const fingerprint = await window.electronAPI.getHardwareFingerprint();
        hardwareFingerprint = fingerprint;
        document.getElementById('hardwareId').textContent = fingerprint;
      } catch (error) {
        document.getElementById('hardwareId').textContent = 'Error loading';
      }
    }

    async function activateLicense() {
      const licenseKey = document.getElementById('licenseKey').value.trim();
      const customerName = document.getElementById('customerName').value.trim();
      const customerEmail = document.getElementById('customerEmail').value.trim();
      const customerCompany = document.getElementById('customerCompany').value.trim();

      if (!licenseKey) {
        showStatus('Please enter a license key', 'error');
        return;
      }

      if (licenseKey.length !== 19) {
        showStatus('License key must be in format XXXX-XXXX-XXXX-XXXX', 'error');
        return;
      }

      const customerInfo = {
        name: customerName,
        email: customerEmail,
        company: customerCompany
      };

      try {
        showStatus('Activating license...', 'success');
        const result = await window.electronAPI.activateLicense(licenseKey, customerInfo);
        
        if (result.success) {
          showStatus('License activated successfully! Starting application...', 'success');
          setTimeout(() => {
            window.electronAPI.startMainApp();
          }, 2000);
        } else {
          showStatus('Activation failed: ' + result.error, 'error');
        }
      } catch (error) {
        showStatus('Activation error: ' + error.message, 'error');
      }
    }

    async function startTrial() {
      try {
        const result = await window.electronAPI.startTrial();
        if (result.success) {
          showStatus('Trial started! Starting application...', 'success');
          setTimeout(() => {
            window.electronAPI.startMainApp();
          }, 2000);
        } else {
          showStatus('Trial start failed: ' + result.error, 'error');
        }
      } catch (error) {
        showStatus('Trial error: ' + error.message, 'error');
      }
    }

    async function continueTrial() {
      window.electronAPI.startMainApp();
    }

    function showStatus(message, type) {
      const statusDiv = document.getElementById('statusMessage');
      statusDiv.textContent = message;
      statusDiv.className = `status-message status-${type}`;
      statusDiv.style.display = 'block';
    }
  </script>
</body>
</html>
const crypto = require('crypto');
const os = require('os');
const fs = require('fs');
const path = require('path');

class LicenseSystem {
  constructor() {
    this.licenseFile = path.join(os.homedir(), '.bannerpro', 'license.dat');
    this.secretKey = 'BannerPro2024SecretKey!@#$%'; // In production, use environment variable
    this.ensureLicenseDirectory();
  }

  ensureLicenseDirectory() {
    const dir = path.dirname(this.licenseFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  // Generate hardware fingerprint
  generateHardwareFingerprint() {
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    
    // Create a unique identifier based on hardware
    const hwInfo = {
      platform: os.platform(),
      arch: os.arch(),
      cpuModel: cpus[0]?.model || 'unknown',
      cpuCount: cpus.length,
      totalMemory: os.totalmem(),
      hostname: os.hostname()
    };

    // Get MAC addresses
    const macAddresses = [];
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      }
    }
    hwInfo.macAddresses = macAddresses.sort();

    // Create hash of hardware info
    const hwString = JSON.stringify(hwInfo);
    return crypto.createHash('sha256').update(hwString).digest('hex').substring(0, 16);
  }

  // Generate license key format: XXXX-XXXX-XXXX-XXXX
  generateLicenseKey(customerInfo = {}) {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');
    const customerHash = crypto.createHash('md5')
      .update(JSON.stringify(customerInfo))
      .digest('hex')
      .substring(0, 8);

    // Create license data
    const licenseData = {
      timestamp,
      customer: customerInfo,
      version: '1.0.0',
      type: 'standard'
    };

    // Create checksum
    const dataString = JSON.stringify(licenseData);
    const checksum = crypto.createHmac('sha256', this.secretKey)
      .update(dataString)
      .digest('hex')
      .substring(0, 8);

    // Format as XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      customerHash.substring(0, 4).toUpperCase(),
      random.substring(0, 4).toUpperCase(),
      timestamp.toString(36).substring(-4).toUpperCase(),
      checksum.substring(0, 4).toUpperCase()
    ];

    return {
      licenseKey: keyParts.join('-'),
      licenseData: licenseData
    };
  }

  // Validate license key format and checksum
  validateLicenseKeyFormat(licenseKey) {
    // Check format XXXX-XXXX-XXXX-XXXX
    const keyPattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
    return keyPattern.test(licenseKey);
  }

  // Activate license (bind to hardware)
  async activateLicense(licenseKey, customerInfo = {}) {
    try {
      if (!this.validateLicenseKeyFormat(licenseKey)) {
        return { success: false, error: 'Invalid license key format' };
      }

      // Check if already activated
      if (this.isLicenseActivated()) {
        const existingLicense = this.getLicenseInfo();
        if (existingLicense.licenseKey === licenseKey) {
          return { success: true, message: 'License already activated on this machine' };
        } else {
          return { success: false, error: 'Different license already activated on this machine' };
        }
      }

      // Generate hardware fingerprint
      const hardwareFingerprint = this.generateHardwareFingerprint();

      // Create activation record
      const activationData = {
        licenseKey: licenseKey,
        hardwareFingerprint: hardwareFingerprint,
        activatedAt: new Date().toISOString(),
        customerInfo: customerInfo,
        version: '1.0.0'
      };

      // Encrypt and save license
      const encryptedData = this.encryptLicenseData(activationData);
      fs.writeFileSync(this.licenseFile, encryptedData);

      return { success: true, message: 'License activated successfully' };

    } catch (error) {
      return { success: false, error: 'Activation failed: ' + error.message };
    }
  }

  // Check if license is activated and valid
  isLicenseActivated() {
    try {
      if (!fs.existsSync(this.licenseFile)) {
        return false;
      }

      const licenseData = this.getLicenseInfo();
      if (!licenseData) {
        return false;
      }

      // Verify hardware fingerprint
      const currentFingerprint = this.generateHardwareFingerprint();
      if (licenseData.hardwareFingerprint !== currentFingerprint) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  // Get license information
  getLicenseInfo() {
    try {
      if (!fs.existsSync(this.licenseFile)) {
        return null;
      }

      const encryptedData = fs.readFileSync(this.licenseFile, 'utf8');
      return this.decryptLicenseData(encryptedData);
    } catch (error) {
      return null;
    }
  }

  // Encrypt license data
  encryptLicenseData(data) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.createHash('sha256').update(this.secretKey).digest();
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  }

  // Decrypt license data
  decryptLicenseData(encryptedData) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.createHash('sha256').update(this.secretKey).digest();

    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  }

  // Remove license (for testing or deactivation)
  removeLicense() {
    try {
      if (fs.existsSync(this.licenseFile)) {
        fs.unlinkSync(this.licenseFile);
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get trial status (30-day trial)
  getTrialStatus() {
    const trialFile = path.join(path.dirname(this.licenseFile), 'trial.dat');
    
    try {
      if (!fs.existsSync(trialFile)) {
        // Start trial
        const trialData = {
          startDate: new Date().toISOString(),
          hardwareFingerprint: this.generateHardwareFingerprint()
        };
        fs.writeFileSync(trialFile, this.encryptLicenseData(trialData));
        return { isTrialActive: true, daysRemaining: 30 };
      }

      const encryptedData = fs.readFileSync(trialFile, 'utf8');
      const trialData = this.decryptLicenseData(encryptedData);
      
      // Verify hardware fingerprint
      if (trialData.hardwareFingerprint !== this.generateHardwareFingerprint()) {
        return { isTrialActive: false, daysRemaining: 0 };
      }

      const startDate = new Date(trialData.startDate);
      const currentDate = new Date();
      const daysPassed = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
      const daysRemaining = Math.max(0, 30 - daysPassed);

      return {
        isTrialActive: daysRemaining > 0,
        daysRemaining: daysRemaining,
        startDate: trialData.startDate
      };

    } catch (error) {
      return { isTrialActive: false, daysRemaining: 0 };
    }
  }
}

module.exports = LicenseSystem;
const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const LicenseSystem = require('./license-system');

// Global error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  dialog.showErrorBox('Application Error', 'An unexpected error occurred. Please restart the application.');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

let bannerWindow;
let controlWindow;
let licenseWindow;
let licenseSystem;

function createWindows() {
  try {
    // Create the banner window
    bannerWindow = new BrowserWindow({
    width: 1920,
    height: 200,
    transparent: true,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Create the control panel window
  controlWindow = new BrowserWindow({
    width: 800,
    height: 600,
    title: 'Banner Pro Control Panel',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  bannerWindow.loadFile('banner.html');
  controlWindow.loadFile('control.html');

  // Position the banner at the bottom of the screen
  try {
    const { screen } = require('electron');
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    bannerWindow.setPosition(0, height - 200);
    bannerWindow.setSize(width, 200);
  } catch (error) {
    console.error('Error positioning banner window:', error);
    // Fallback positioning
    bannerWindow.setPosition(0, 800);
  }
  // When control window is closed, close the banner window and quit the app
  controlWindow.on('closed', () => {
    controlWindow = null;
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.close();
    }
    bannerWindow = null;
    if (!app.isQuiting) {
      app.isQuiting = true;
      app.quit();
    }
  });

  // If banner window is closed, close control window and quit the app
  bannerWindow.on('closed', () => {
    bannerWindow = null;
    if (controlWindow && !controlWindow.isDestroyed()) {
      controlWindow.close();
    }
    controlWindow = null;
    if (!app.isQuiting) {
      app.isQuiting = true;
      app.quit();
    }
  });

  } catch (error) {
    console.error('Error creating windows:', error);
    dialog.showErrorBox('Startup Error', 'Failed to create application windows. Please try restarting the application.');
    app.quit();
  }
}

// Check license and start appropriate window
function checkLicenseAndStart() {
  try {
    const isLicensed = licenseSystem.isLicenseActivated();
    const trialStatus = licenseSystem.getTrialStatus();

    if (isLicensed) {
      // Valid license, start main app
      createWindows();
    } else if (trialStatus.isTrialActive) {
      // Trial is active, start main app
      createWindows();
    } else {
      // No valid license or trial, show activation window
      createLicenseWindow();
    }
  } catch (error) {
    console.error('Error checking license:', error);
    // Fallback to license window
    createLicenseWindow();
  }
}

// Create license activation window
function createLicenseWindow() {
  try {
    licenseWindow = new BrowserWindow({
      width: 600,
      height: 700,
      title: 'Banner Pro - License Activation',
      resizable: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    licenseWindow.loadFile('license-activation.html');

    licenseWindow.on('closed', () => {
      licenseWindow = null;
      if (!bannerWindow && !controlWindow) {
        app.quit();
      }
    });

  } catch (error) {
    console.error('Error creating license window:', error);
    dialog.showErrorBox('Startup Error', 'Failed to create license activation window.');
    app.quit();
  }
}

app.whenReady().then(() => {
  licenseSystem = new LicenseSystem();
  checkLicenseAndStart();
});

app.on('window-all-closed', () => {
  // Clean up references
  bannerWindow = null;
  controlWindow = null;

  if (process.platform !== 'darwin') {
    if (!app.isQuiting) {
      app.isQuiting = true;
      app.quit();
    }
  }
});

app.on('before-quit', () => {
  app.isQuiting = true;
});

// Handle app activation (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindows();
  }
});

// Handle settings updates
ipcMain.on('update-settings', (event, settings) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.webContents.send('apply-settings', settings);
    }
  } catch (error) {
    console.log('Error sending settings to banner window:', error.message);
  }
});

// Handle banner visibility toggle
ipcMain.on('toggle-banner', (event) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      if (bannerWindow.isVisible()) {
        bannerWindow.hide();
        event.reply('banner-visibility-changed', false);
      } else {
        bannerWindow.show();
        event.reply('banner-visibility-changed', true);
      }
    }
  } catch (error) {
    console.log('Error toggling banner visibility:', error.message);
  }
});

// Handle banner show/hide requests
ipcMain.on('show-banner', (event) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.show();
      event.reply('banner-visibility-changed', true);
    }
  } catch (error) {
    console.log('Error showing banner:', error.message);
  }
});

ipcMain.on('hide-banner', (event) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.hide();
      event.reply('banner-visibility-changed', false);
    }
  } catch (error) {
    console.log('Error hiding banner:', error.message);
  }
});

// License system IPC handlers
ipcMain.handle('check-license', async () => {
  try {
    return {
      isActivated: licenseSystem.isLicenseActivated(),
      licenseInfo: licenseSystem.getLicenseInfo()
    };
  } catch (error) {
    return { isActivated: false, error: error.message };
  }
});

ipcMain.handle('activate-license', async (event, licenseKey, customerInfo) => {
  try {
    return await licenseSystem.activateLicense(licenseKey, customerInfo);
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-trial-status', async () => {
  try {
    return licenseSystem.getTrialStatus();
  } catch (error) {
    return { isTrialActive: false, daysRemaining: 0, error: error.message };
  }
});

ipcMain.handle('start-trial', async () => {
  try {
    const trialStatus = licenseSystem.getTrialStatus();
    if (trialStatus.isTrialActive || trialStatus.daysRemaining > 0) {
      return { success: true, message: 'Trial is active' };
    } else {
      return { success: false, error: 'Trial period has expired' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-hardware-fingerprint', async () => {
  try {
    return licenseSystem.generateHardwareFingerprint();
  } catch (error) {
    return 'Error generating fingerprint';
  }
});

ipcMain.on('start-main-app', () => {
  try {
    if (licenseWindow) {
      licenseWindow.close();
      licenseWindow = null;
    }
    createWindows();
  } catch (error) {
    console.error('Error starting main app:', error);
  }
});

{
  "name": "banner-pro",
  "version": "1.0.0",
  "description": "Professional advertisement banner and digital signage solution for businesses, events, and organizations",
  "main": "main.js",
  "author": "Banner Pro <<EMAIL>>",
  "license": "Commercial",
  "homepage": "https://bannerpro.com",
  "repository": {
    "type": "git",
    "url": "https://github.com/bannerpro/banner-pro.git"
  }
}const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // For the control panel
  updateSettings: (settings) => ipcRenderer.send('update-settings', settings),

  // Banner visibility controls
  toggleBanner: () => ipcRenderer.send('toggle-banner'),
  showBanner: () => ipcRenderer.send('show-banner'),
  hideBanner: () => ipcRenderer.send('hide-banner'),
  onBannerVisibilityChanged: (callback) => ipcRenderer.on('banner-visibility-changed', callback),

  // License system functions
  checkLicense: () => ipcRenderer.invoke('check-license'),
  activateLicense: (licenseKey, customerInfo) => ipcRenderer.invoke('activate-license', licenseKey, customerInfo),
  getTrialStatus: () => ipcRenderer.invoke('get-trial-status'),
  startTrial: () => ipcRenderer.invoke('start-trial'),
  getHardwareFingerprint: () => ipcRenderer.invoke('get-hardware-fingerprint'),
  startMainApp: () => ipcRenderer.send('start-main-app'),

  // For the banner window
  onApplySettings: (callback) => ipcRenderer.on('apply-settings', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // File system operations (for image loading)
  readFile: (filePath) => {
    const fs = require('fs');
    try {
      if (!filePath || typeof filePath !== 'string') {
        throw new Error('Invalid file path provided');
      }
      return fs.readFileSync(filePath);
    } catch (error) {
      console.error('Error reading file:', error.message);
      return null;
    }
  },
  
  // Path operations
  join: (...paths) => {
    const path = require('path');
    return path.join(...paths);
  },
  
  // Check if file exists
  fileExists: (filePath) => {
    const fs = require('fs');
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      return false;
    }
  }
});
