{"version": 3, "file": "filename.js", "sourceRoot": "", "sources": ["../../src/util/filename.ts"], "names": [], "mappings": ";;;AAAA,aAAa;AACb,uDAAsD;AACtD,6BAA4B;AAE5B,SAAgB,gBAAgB,CAAC,CAAS,EAAE,YAAY,GAAG,KAAK;IAC9D,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;IACtC,OAAO,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;AAC9D,CAAC;AAHD,4CAGC;AAED,qFAAqF;AACrF,yFAAyF;AACzF,oFAAoF;AACpF,gCAAgC;AAChC,SAAgB,kBAAkB,CAAC,QAAgB;IACjD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAEpC,QAAQ,OAAO,EAAE,CAAC;QAChB,iDAAiD;QACjD,KAAK,WAAW,CAAC,CAAC,CAAC;YACjB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAA;YAE/D,MAAK;QACP,CAAC;QACD,4DAA4D;QAC5D,KAAK,MAAM,CAAC;QACZ,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC;QACX,KAAK,KAAK,CAAC,CAAC,CAAC;YACX,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;YACvD,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;gBACnB,OAAO,GAAG,GAAG,GAAG,OAAO,CAAA;YACzB,CAAC;YAED,MAAK;QACP,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AA1BD,gDA0BC", "sourcesContent": ["// @ts-ignore\nimport * as _sanitize<PERSON>ileName from \"sanitize-filename\"\nimport * as path from \"path\"\n\nexport function sanitizeFileName(s: string, normalizeNfd = false): string {\n  const sanitized = _sanitizeFileName(s)\n  return normalizeNfd ? sanitized.normalize(\"NFD\") : sanitized\n}\n\n// Get the filetype from a filename. Returns a string of one or more file extensions,\n// e.g. .zip, .dmg, .tar.gz, .tar.bz2, .exe.blockmap. We'd normally use `path.extname()`,\n// but it doesn't support multiple extensions, e.g. Foo-1.0.0.dmg.blockmap should be\n// .dmg.blockmap, not .blockmap.\nexport function getCompleteExtname(filename: string): string {\n  let extname = path.extname(filename)\n\n  switch (extname) {\n    // Append leading extension for blockmap filetype\n    case \".blockmap\": {\n      extname = path.extname(filename.replace(extname, \"\")) + extname\n\n      break\n    }\n    // Append leading extension for known compressed tar formats\n    case \".bz2\":\n    case \".gz\":\n    case \".lz\":\n    case \".xz\":\n    case \".7z\": {\n      const ext = path.extname(filename.replace(extname, \"\"))\n      if (ext === \".tar\") {\n        extname = ext + extname\n      }\n\n      break\n    }\n  }\n\n  return extname\n}\n"]}