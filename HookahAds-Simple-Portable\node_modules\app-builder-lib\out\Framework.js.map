{"version": 3, "file": "Framework.js", "sourceRoot": "", "sources": ["../src/Framework.ts"], "names": [], "mappings": ";;;AAmDA,SAAgB,eAAe,CAAC,SAAoB;IAClD,OAAO,SAAS,CAAC,IAAI,KAAK,UAAU,CAAA;AACtC,CAAC;AAFD,0CAEC", "sourcesContent": ["import { FileTransformer } from \"builder-util/out/fs\"\nimport { AsarIntegrity } from \"./asar/integrity\"\nimport { Platform, PlatformPackager, ElectronPlatformName, AfterPackContext } from \"./index\"\n\nexport interface Framework {\n  readonly name: string\n  readonly version: string\n  readonly distMacOsAppName: string\n  readonly macOsDefaultTargets: Array<string>\n  readonly defaultAppIdPrefix: string\n\n  readonly isNpmRebuildRequired: boolean\n\n  readonly isCopyElevateHelper: boolean\n\n  getDefaultIcon?(platform: Platform): string | null\n\n  getMainFile?(platform: Platform): string | null\n\n  getExcludedDependencies?(platform: Platform): Array<string> | null\n\n  prepareApplicationStageDirectory(options: PrepareApplicationStageDirectoryOptions): Promise<any>\n\n  beforeCopyExtraFiles?(options: BeforeCopyExtraFilesOptions): Promise<any>\n\n  afterPack?(context: AfterPackContext): Promise<any>\n\n  createTransformer?(): FileTransformer | null\n}\n\nexport interface BeforeCopyExtraFilesOptions {\n  packager: PlatformPackager<any>\n  appOutDir: string\n\n  asarIntegrity: AsarIntegrity | null\n\n  // ElectronPlatformName\n  platformName: string\n}\n\nexport interface PrepareApplicationStageDirectoryOptions {\n  readonly packager: PlatformPackager<any>\n  /**\n   * Platform doesn't process application output directory in any way. Unpack implementation must create or empty dir if need.\n   */\n  readonly appOutDir: string\n  readonly platformName: ElectronPlatformName\n  readonly arch: string\n  readonly version: string\n}\n\nexport function isElectronBased(framework: Framework): boolean {\n  return framework.name === \"electron\"\n}\n"]}