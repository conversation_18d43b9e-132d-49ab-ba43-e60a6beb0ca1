{"version": 3, "file": "langs.js", "sourceRoot": "", "sources": ["../../src/util/langs.ts"], "names": [], "mappings": ";;;AAAa,QAAA,gBAAgB,GAAG;IAC9B,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;CACR,CAAA;AAED,uCAAuC;AAEvC,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAkB,CAAA;AACtD,KAAK,MAAM,EAAE,IAAI,wBAAgB,EAAE,CAAC;IAClC,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;AAChE,CAAC;AAED,SAAgB,gBAAgB,CAAC,IAAY;IAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;IAEzB,MAAM,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC7C,OAAO,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;AAClE,CAAC;AATD,4CASC;AAEY,QAAA,IAAI,GAAQ;IACvB,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,KAAK;IACb,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;CACZ,CAAA;AAED,uCAAuC;AAC1B,QAAA,YAAY,GAAG;IAC1B,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,eAAe;IACnB,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,cAAc;IAClB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,kBAAkB;IACtB,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,kBAAkB;IACtB,EAAE,EAAE,mBAAmB;IACvB,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,kBAAkB;IACtB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,qBAAqB;IACzB,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,eAAe;IACnB,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,gBAAgB;IACpB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,kBAAkB;IACtB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,UAAU;IACd,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,WAAW;IACf,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,YAAY;IAChB,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,iBAAiB;IACrB,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,SAAS;IACb,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,QAAQ;IACZ,EAAE,EAAE,MAAM;CACX,CAAA", "sourcesContent": ["export const bundledLanguages = [\n  \"en_US\",\n  \"de_DE\",\n  \"fr_FR\",\n  \"es_ES\",\n  \"zh_CN\",\n  \"zh_TW\",\n  \"ja_JP\",\n  \"ko_KR\",\n  \"it_IT\",\n  \"nl_NL\",\n  \"da_DK\",\n  \"sv_SE\",\n  \"nb_NO\",\n  \"fi_FI\",\n  \"ru_RU\",\n  \"pt_PT\",\n  \"pt_BR\",\n  \"pl_PL\",\n  \"uk_UA\",\n  \"cs_CZ\",\n  \"sk_SK\",\n  \"hu_HU\",\n  \"ar_SA\",\n  \"tr_TR\",\n  \"th_TH\",\n  \"vi_VN\",\n]\n\n// todo \"ro_RO\" \"el_GR\" \"et_EE\" \"ka_GE\"\n\nconst langToLangWithRegion = new Map<string, string>()\nfor (const id of bundledLanguages) {\n  langToLangWithRegion.set(id.substring(0, id.indexOf(\"_\")), id)\n}\n\nexport function toLangWithRegion(lang: string): string {\n  if (lang.includes(\"_\")) {\n    return lang\n  }\n\n  lang = lang.toLowerCase()\n\n  const result = langToLangWithRegion.get(lang)\n  return result == null ? `${lang}_${lang.toUpperCase()}` : result\n}\n\nexport const lcid: any = {\n  af_ZA: 1078,\n  am_ET: 1118,\n  ar_AE: 14337,\n  ar_BH: 15361,\n  ar_DZ: 5121,\n  ar_EG: 3073,\n  ar_IQ: 2049,\n  ar_JO: 11265,\n  ar_KW: 13313,\n  ar_LB: 12289,\n  ar_LY: 4097,\n  ar_MA: 6145,\n  ar_OM: 8193,\n  ar_QA: 16385,\n  ar_SA: 1025,\n  ar_SY: 10241,\n  ar_TN: 7169,\n  ar_YE: 9217,\n  arn_CL: 1146,\n  as_IN: 1101,\n  az_AZ: 2092,\n  ba_RU: 1133,\n  be_BY: 1059,\n  bg_BG: 1026,\n  bn_IN: 1093,\n  bo_BT: 2129,\n  bo_CN: 1105,\n  br_FR: 1150,\n  bs_BA: 8218,\n  ca_ES: 1027,\n  co_FR: 1155,\n  cs_CZ: 1029,\n  cy_GB: 1106,\n  da_DK: 1030,\n  de_AT: 3079,\n  de_CH: 2055,\n  de_DE: 1031,\n  de_LI: 5127,\n  de_LU: 4103,\n  div_MV: 1125,\n  dsb_DE: 2094,\n  el_GR: 1032,\n  en_AU: 3081,\n  en_BZ: 10249,\n  en_CA: 4105,\n  en_CB: 9225,\n  en_GB: 2057,\n  en_IE: 6153,\n  en_IN: 18441,\n  en_JA: 8201,\n  en_MY: 17417,\n  en_NZ: 5129,\n  en_PH: 13321,\n  en_TT: 11273,\n  en_US: 1033,\n  en_ZA: 7177,\n  en_ZW: 12297,\n  es_AR: 11274,\n  es_BO: 16394,\n  es_CL: 13322,\n  es_CO: 9226,\n  es_CR: 5130,\n  es_DO: 7178,\n  es_EC: 12298,\n  es_ES: 3082,\n  es_GT: 4106,\n  es_HN: 18442,\n  es_MX: 2058,\n  es_NI: 19466,\n  es_PA: 6154,\n  es_PE: 10250,\n  es_PR: 20490,\n  es_PY: 15370,\n  es_SV: 17418,\n  es_UR: 14346,\n  es_US: 21514,\n  es_VE: 8202,\n  et_EE: 1061,\n  eu_ES: 1069,\n  fa_IR: 1065,\n  fi_FI: 1035,\n  fil_PH: 1124,\n  fo_FO: 1080,\n  fr_BE: 2060,\n  fr_CA: 3084,\n  fr_CH: 4108,\n  fr_FR: 1036,\n  fr_LU: 5132,\n  fr_MC: 6156,\n  fy_NL: 1122,\n  ga_IE: 2108,\n  gbz_AF: 1164,\n  gl_ES: 1110,\n  gsw_FR: 1156,\n  gu_IN: 1095,\n  ha_NG: 1128,\n  he_IL: 1037,\n  hi_IN: 1081,\n  hr_BA: 4122,\n  hr_HR: 1050,\n  hu_HU: 1038,\n  hy_AM: 1067,\n  id_ID: 1057,\n  ii_CN: 1144,\n  is_IS: 1039,\n  it_CH: 2064,\n  it_IT: 1040,\n  iu_CA: 2141,\n  ja_JP: 1041,\n  ka_GE: 1079,\n  kh_KH: 1107,\n  kk_KZ: 1087,\n  kl_GL: 1135,\n  kn_IN: 1099,\n  ko_KR: 1042,\n  kok_IN: 1111,\n  ky_KG: 1088,\n  lb_LU: 1134,\n  lo_LA: 1108,\n  lt_LT: 1063,\n  lv_LV: 1062,\n  mi_NZ: 1153,\n  mk_MK: 1071,\n  ml_IN: 1100,\n  mn_CN: 2128,\n  mn_MN: 1104,\n  moh_CA: 1148,\n  mr_IN: 1102,\n  ms_BN: 2110,\n  ms_MY: 1086,\n  mt_MT: 1082,\n  my_MM: 1109,\n  nb_NO: 1044,\n  ne_NP: 1121,\n  nl_BE: 2067,\n  nl_NL: 1043,\n  nn_NO: 2068,\n  ns_ZA: 1132,\n  oc_FR: 1154,\n  or_IN: 1096,\n  pa_IN: 1094,\n  pl_PL: 1045,\n  ps_AF: 1123,\n  pt_BR: 1046,\n  pt_PT: 2070,\n  qut_GT: 1158,\n  quz_BO: 1131,\n  quz_EC: 2155,\n  quz_PE: 3179,\n  rm_CH: 1047,\n  ro_RO: 1048,\n  ru_RU: 1049,\n  rw_RW: 1159,\n  sa_IN: 1103,\n  sah_RU: 1157,\n  se_FI: 3131,\n  se_NO: 1083,\n  se_SE: 2107,\n  si_LK: 1115,\n  sk_SK: 1051,\n  sl_SI: 1060,\n  sma_NO: 6203,\n  sma_SE: 7227,\n  smj_NO: 4155,\n  smj_SE: 5179,\n  smn_FI: 9275,\n  sms_FI: 8251,\n  sq_AL: 1052,\n  sr_BA: 7194,\n  sr_SP: 3098,\n  sv_FI: 2077,\n  sv_SE: 1053,\n  sw_KE: 1089,\n  syr_SY: 1114,\n  ta_IN: 1097,\n  te_IN: 1098,\n  tg_TJ: 1064,\n  th_TH: 1054,\n  tk_TM: 1090,\n  tmz_DZ: 2143,\n  tn_ZA: 1074,\n  tr_TR: 1055,\n  tt_RU: 1092,\n  ug_CN: 1152,\n  uk_UA: 1058,\n  ur_IN: 2080,\n  ur_PK: 1056,\n  uz_UZ: 2115,\n  vi_VN: 1066,\n  wen_DE: 1070,\n  wo_SN: 1160,\n  xh_ZA: 1076,\n  yo_NG: 1130,\n  zh_CHS: 4,\n  zh_CHT: 31748,\n  zh_CN: 2052,\n  zh_HK: 3076,\n  zh_MO: 5124,\n  zh_SG: 4100,\n  zh_TW: 1028,\n  zu_ZA: 1077,\n}\n\n// noinspection SpellCheckingInspection\nexport const langIdToName = {\n  ab: \"Abkhaz\",\n  aa: \"Afar\",\n  af: \"Afrikaans\",\n  ak: \"Akan\",\n  sq: \"Albanian\",\n  am: \"Amharic\",\n  ar: \"Arabic\",\n  an: \"Aragonese\",\n  hy: \"Armenian\",\n  as: \"Assamese\",\n  av: \"Avaric\",\n  ae: \"Avestan\",\n  ay: \"Aymara\",\n  az: \"Azerbaijani\",\n  bm: \"Bambara\",\n  ba: \"Bashkir\",\n  eu: \"Basque\",\n  be: \"Belarusian\",\n  bn: \"Bengali\",\n  bh: \"Bihari\",\n  bi: \"Bislama\",\n  bs: \"Bosnian\",\n  br: \"Breton\",\n  bg: \"Bulgarian\",\n  my: \"Burmese\",\n  ca: \"Catalan\",\n  ch: \"Chamorro\",\n  ce: \"Chechen\",\n  ny: \"Chichewa\",\n  zh: \"Chinese\",\n  cv: \"Chuvash\",\n  kw: \"Cornish\",\n  co: \"Corsican\",\n  cr: \"Cree\",\n  hr: \"Croatian\",\n  cs: \"Czech\",\n  da: \"Danish\",\n  dv: \"Divehi\",\n  nl: \"Dutch\",\n  dz: \"Dzongkha\",\n  en: \"English\",\n  eo: \"Esperanto\",\n  et: \"Estonian\",\n  ee: \"Ewe\",\n  fo: \"Faroese\",\n  fj: \"Fijian\",\n  fi: \"Finnish\",\n  fr: \"French\",\n  ff: \"Fula\",\n  gl: \"Galician\",\n  ka: \"Georgian\",\n  de: \"German\",\n  el: \"Greek\",\n  gn: \"Guaraní\",\n  gu: \"Gujarati\",\n  ht: \"Haitian\",\n  ha: \"Hausa\",\n  he: \"Hebrew\",\n  hz: \"Herero\",\n  hi: \"Hindi\",\n  ho: \"Hiri Motu\",\n  hu: \"Hungarian\",\n  ia: \"Interlingua\",\n  id: \"Indonesian\",\n  ie: \"Interlingue\",\n  ga: \"Irish\",\n  ig: \"Igbo\",\n  ik: \"Inupiaq\",\n  io: \"Ido\",\n  is: \"Icelandic\",\n  it: \"Italian\",\n  iu: \"Inuktitut\",\n  ja: \"Japanese\",\n  jv: \"Javanese\",\n  kl: \"Kalaallisut\",\n  kn: \"Kannada\",\n  kr: \"Kanuri\",\n  ks: \"Kashmiri\",\n  kk: \"Kazakh\",\n  km: \"Khmer\",\n  ki: \"Kikuyu\",\n  rw: \"Kinyarwanda\",\n  ky: \"Kyrgyz\",\n  kv: \"Komi\",\n  kg: \"Kongo\",\n  ko: \"Korean\",\n  ku: \"Kurdish\",\n  kj: \"Kwanyama\",\n  la: \"Latin\",\n  lb: \"Luxembourgish\",\n  lg: \"Ganda\",\n  li: \"Limburgish\",\n  ln: \"Lingala\",\n  lo: \"Lao\",\n  lt: \"Lithuanian\",\n  lu: \"Luba-Katanga\",\n  lv: \"Latvian\",\n  gv: \"Manx\",\n  mk: \"Macedonian\",\n  mg: \"Malagasy\",\n  ms: \"Malay\",\n  ml: \"Malayalam\",\n  mt: \"Maltese\",\n  mi: \"Māori\",\n  mr: \"Marathi\",\n  mh: \"Marshallese\",\n  mn: \"Mongolian\",\n  na: \"Nauru\",\n  nv: \"Navajo\",\n  nd: \"Northern Ndebele\",\n  ne: \"Nepali\",\n  ng: \"Ndonga\",\n  nb: \"Norwegian Bokmål\",\n  nn: \"Norwegian Nynorsk\",\n  no: \"Norwegian\",\n  ii: \"Nuosu\",\n  nr: \"Southern Ndebele\",\n  oc: \"Occitan\",\n  oj: \"Ojibwe\",\n  cu: \"Old Church Slavonic\",\n  om: \"Oromo\",\n  or: \"Oriya\",\n  os: \"Ossetian\",\n  pa: \"Panjabi\",\n  pi: \"Pāli\",\n  fa: \"Persian\",\n  pl: \"Polish\",\n  ps: \"Pashto\",\n  pt: \"Portuguese\",\n  qu: \"Quechua\",\n  rm: \"Romansh\",\n  rn: \"Kirundi\",\n  ro: \"Romanian\",\n  ru: \"Russian\",\n  sa: \"Sanskrit\",\n  sc: \"Sardinian\",\n  sd: \"Sindhi\",\n  se: \"Northern Sami\",\n  sm: \"Samoan\",\n  sg: \"Sango\",\n  sr: \"Serbian\",\n  gd: \"Gaelic\",\n  sn: \"Shona\",\n  si: \"Sinhala\",\n  sk: \"Slovak\",\n  sl: \"Slovene\",\n  so: \"Somali\",\n  st: \"Southern Sotho\",\n  es: \"Spanish\",\n  su: \"Sundanese\",\n  sw: \"Swahili\",\n  ss: \"Swati\",\n  sv: \"Swedish\",\n  ta: \"Tamil\",\n  te: \"Telugu\",\n  tg: \"Tajik\",\n  th: \"Thai\",\n  ti: \"Tigrinya\",\n  bo: \"Tibetan Standard\",\n  tk: \"Turkmen\",\n  tl: \"Tagalog\",\n  tn: \"Tswana\",\n  to: \"Tonga\",\n  tr: \"Turkish\",\n  ts: \"Tsonga\",\n  tt: \"Tatar\",\n  tw: \"Twi\",\n  ty: \"Tahitian\",\n  ug: \"Uyghur\",\n  uk: \"Ukrainian\",\n  ur: \"Urdu\",\n  uz: \"Uzbek\",\n  ve: \"Venda\",\n  vi: \"Vietnamese\",\n  vo: \"Volapük\",\n  wa: \"Walloon\",\n  cy: \"Welsh\",\n  wo: \"Wolof\",\n  fy: \"Western Frisian\",\n  xh: \"Xhosa\",\n  yi: \"Yiddish\",\n  yo: \"Yoruba\",\n  za: \"Zhuang\",\n  zu: \"Zulu\",\n}\n"]}