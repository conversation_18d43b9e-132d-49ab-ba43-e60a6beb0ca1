<!DOCTYPE html>
<html>
<head>
  <title>Banner Pro Control Panel</title>
  <style>
    * {
      box-sizing: border-box;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .header {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 28px;
      font-weight: 300;
    }
    .header p {
      color: rgba(255, 255, 255, 0.8);
      margin: 5px 0 0 0;
      font-size: 14px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .control-group {
      background: rgba(255, 255, 255, 0.95);
      margin-bottom: 20px;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .control-group h3 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }
    label {
      display: block;
      margin-bottom: 8px;
      color: #555;
      font-weight: 500;
      font-size: 14px;
    }
    input, button, select, textarea {
      margin: 5px 0;
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
    }
    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      padding: 12px 24px;
      border: none;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    button:active {
      transform: translateY(0);
    }
    input[type="range"] {
      width: 100%;
      height: 6px;
      background: #e1e5e9;
      outline: none;
      border-radius: 3px;
      border: none;
    }
    input[type="range"]::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      background: #667eea;
      cursor: pointer;
      border-radius: 50%;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }
    .template-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 10px;
      margin-bottom: 15px;
    }
    .template-buttons button {
      padding: 10px 15px;
      font-size: 14px;
      border-radius: 6px;
    }
    .apply-button {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      font-size: 18px;
      padding: 15px 30px;
      margin-top: 20px;
      width: 100%;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Banner Pro</h1>
    <p>Professional Digital Signage Control Panel</p>
  </div>

  <div class="container">
    <div class="control-group">
      <h3>👁️ Banner Visibility</h3>
      <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
        <button id="toggleBannerBtn" onclick="toggleBanner()" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);">🔄 Toggle Banner</button>
        <button onclick="showBanner()" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);">👁️ Show Banner</button>
        <button onclick="hideBanner()" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">🙈 Hide Banner</button>
        <span id="bannerStatus" style="margin-left: 10px; font-weight: bold; color: #4CAF50;">Banner: Visible</span>
      </div>
      <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
        💡 Tip: Hide the banner when you need to click buttons at the bottom of your screen<br>
        ⌨️ Quick toggle: Press <strong>Ctrl+B</strong>
      </div>
    </div>

    <div class="control-group">
      <h3>📋 Templates</h3>
      <div class="template-buttons">
        <button onclick="loadTemplate(1)">📋 Load Template 1</button>
        <button onclick="loadTemplate(2)">📋 Load Template 2</button>
        <button onclick="loadTemplate(3)">📋 Load Template 3</button>
      </div>
      <div class="template-buttons">
        <button onclick="saveTemplate(1)">💾 Save Template 1</button>
        <button onclick="saveTemplate(2)">💾 Save Template 2</button>
        <button onclick="saveTemplate(3)">💾 Save Template 3</button>
      </div>
    <div id="templateStatus" style="margin-top: 5px; font-size: 12px; color: #666;">
      Templates: Save your current settings or load previously saved configurations
    </div>
  </div>

  <div class="control-group">
    <h3>⚙️ Sequence Configuration</h3>
    <label>Number of Sequences:</label>
    <select id="sequenceCount" onchange="updateSequenceInputs()" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="1">1 Sequence</option>
      <option value="2">2 Sequences</option>
      <option value="3">3 Sequences</option>
    </select>
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      Choose how many different advertisement sequences you want
    </div>
  </div>

  <!-- Sequence 1 -->
  <div class="sequence-section" id="sequence1" style="border: 2px solid #4CAF50; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9fff9;">
    <h3 style="margin-top: 0; color: #4CAF50;">📝 Sequence 1</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text1" placeholder="Enter text for sequence 1" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement1" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual1" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload1" accept="image/*">
      <button onclick="clearPicture(1)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview1" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <!-- Sequence 2 (hidden by default) -->
  <div class="sequence-section" id="sequence2" style="border: 2px solid #2196F3; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9f9ff; display: none;">
    <h3 style="margin-top: 0; color: #2196F3;">📝 Sequence 2</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text2" placeholder="Enter text for sequence 2" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement2" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual2" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload2" accept="image/*">
      <button onclick="clearPicture(2)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview2" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <!-- Sequence 3 (hidden by default) -->
  <div class="sequence-section" id="sequence3" style="border: 2px solid #FF9800; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #fffaf9; display: none;">
    <h3 style="margin-top: 0; color: #FF9800;">📝 Sequence 3</h3>
    <div class="control-group">
      <label>Text:</label>
      <input type="text" id="text3" placeholder="Enter text for sequence 3" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Visual Element:</label>
      <select id="visualElement3" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">No visual element</option>
        <option value="🔥">🔥 Fire (Hot/Popular)</option>
        <option value="⭐">⭐ Star (Premium/Quality)</option>
        <option value="🎉">🎉 Party (Celebration/Fun)</option>
        <option value="💎">💎 Diamond (Luxury/VIP)</option>
        <option value="🌟">🌟 Sparkle (Special/New)</option>
        <option value="🏆">🏆 Trophy (Best/Winner)</option>
        <option value="❤️">❤️ Heart (Love/Favorite)</option>
        <option value="🎯">🎯 Target (Special Offer)</option>
        <option value="⚡">⚡ Lightning (Fast/Energy)</option>
        <option value="🎪">🎪 Circus (Fun/Entertainment)</option>
        <option value="🌙">🌙 Moon (Night/Evening)</option>
        <option value="☀️">☀️ Sun (Day/Bright)</option>
        <option value="🎵">🎵 Music (Entertainment)</option>
        <option value="🍃">🍃 Leaf (Fresh/Natural)</option>
        <option value="💫">💫 Dizzy (Amazing/Wow)</option>
      </select>
    </div>
    <div class="control-group">
      <label>Custom Visual Text:</label>
      <input type="text" id="customVisual3" placeholder="★★★ PREMIUM ★★★, ═══ SPECIAL ═══" style="width: 100%;">
    </div>
    <div class="control-group">
      <label>Picture:</label>
      <input type="file" id="pictureUpload3" accept="image/*">
      <button onclick="clearPicture(3)" style="margin-left: 10px; padding: 5px 10px;">Clear</button>
      <div id="picturePreview3" style="margin-top: 10px; padding: 10px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; min-height: 60px; background-color: #f9f9f9; font-size: 12px; color: #666;">
        No picture uploaded
      </div>
    </div>
  </div>

  <div class="control-group">
    <label>Banner Height (2cm - 8cm):</label>
    <input type="range" id="height" min="75" max="300" value="75">
    <span id="heightValue">2cm</span>
  </div>

  <div class="control-group">
    <label>Background Color:</label>
    <input type="color" id="backgroundColor" value="#000000">
  </div>

  <div class="control-group">
    <label>Text Color:</label>
    <input type="color" id="textColor" value="#ffffff">
  </div>

  <div class="control-group">
    <label>Font Size:</label>
    <select id="fontSize" style="width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
      <option value="50">Small (50% of banner height)</option>
      <option value="60">Medium-Small (60% of banner height)</option>
      <option value="70" selected>Medium (70% of banner height)</option>
      <option value="80">Medium-Large (80% of banner height)</option>
      <option value="90">Large (90% of banner height)</option>
    </select>
  </div>

  <div class="control-group">
    <label>Scroll Speed (seconds per cycle):</label>
    <input type="range" id="scrollSpeed" min="15" max="120" value="30">
    <span id="speedValue">30s</span>
    <div style="margin-top: 5px; font-size: 12px; color: #666;">
      15s = Fast • 30s = Normal • 60s = Slow • 120s = Very Slow
    </div>
  </div>





    <button onclick="applySettings()" class="apply-button">🚀 Apply Changes</button>
  </div>

  <script>
    // Use the secure API exposed through preload script
    let selectedImages = [];
    let bannerVisible = true;

    // Height slider
    const heightSlider = document.getElementById('height');
    const heightValue = document.getElementById('heightValue');
    heightSlider.oninput = () => {
      const cm = (heightSlider.value / 37.5).toFixed(1);
      heightValue.textContent = cm + 'cm';
    };

    // Speed slider
    const speedSlider = document.getElementById('scrollSpeed');
    const speedValue = document.getElementById('speedValue');
    speedSlider.oninput = () => {
      speedValue.textContent = speedSlider.value + 's';
    };

    // Multiple sequences handling
    let sequences = [
      { text: '', visualElement: '', customVisual: '', picture: null },
      { text: '', visualElement: '', customVisual: '', picture: null },
      { text: '', visualElement: '', customVisual: '', picture: null }
    ];

    // Show/hide sequence inputs based on count
    function updateSequenceInputs() {
      const count = parseInt(document.getElementById('sequenceCount').value);

      for (let i = 1; i <= 3; i++) {
        const sequenceDiv = document.getElementById(`sequence${i}`);
        if (i <= count) {
          sequenceDiv.style.display = 'block';
        } else {
          sequenceDiv.style.display = 'none';
        }
      }
    }

    // Picture upload handling for multiple sequences
    function setupPictureUpload(sequenceNum) {
      document.getElementById(`pictureUpload${sequenceNum}`).onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(event) {
            sequences[sequenceNum - 1].picture = event.target.result;

            // Show preview
            const preview = document.getElementById(`picturePreview${sequenceNum}`);
            preview.innerHTML = `
              <img src="${sequences[sequenceNum - 1].picture}" style="max-width: 150px; max-height: 60px; border-radius: 4px;">
              <div style="margin-top: 5px; font-size: 10px; color: #4CAF50; font-weight: bold;">${file.name}</div>
            `;
          };
          reader.readAsDataURL(file);
        }
      };
    }

    // Clear picture function for sequences
    function clearPicture(sequenceNum) {
      sequences[sequenceNum - 1].picture = null;
      document.getElementById(`pictureUpload${sequenceNum}`).value = '';
      document.getElementById(`picturePreview${sequenceNum}`).innerHTML = 'No picture uploaded';
    }

    // Setup picture uploads for all sequences
    setupPictureUpload(1);
    setupPictureUpload(2);
    setupPictureUpload(3);

    // Template management
    let templates = {
      1: null,
      2: null,
      3: null
    };

    // Load templates from localStorage (simplified)
    function loadTemplatesFromStorage() {
      try {
        const savedTemplates = localStorage.getItem('bannerProTemplates');
        if (savedTemplates) {
          templates = JSON.parse(savedTemplates);
          console.log('Templates loaded from localStorage');
        } else {
          createDefaultTemplates();
        }
      } catch (error) {
        console.error('Error loading templates:', error);
        createDefaultTemplates();
      }
    }

    // Create default templates
    function createDefaultTemplates() {
      templates = {
        1: {
          name: "Store Hours & Info",
          sequenceCount: 2,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "Open Daily 12PM-2AM • Premium Service Experience",
              visualElement: "🌙",
              customVisual: "✦✧✦ OPEN LATE ✦✧✦",
              picture: null
            },
            {
              text: "Best Quality Tobacco • Relaxing Atmosphere",
              visualElement: "🍃",
              customVisual: "★★★ PREMIUM ★★★",
              picture: null
            }
          ]
        },
        2: {
          name: "Special Offers",
          sequenceCount: 3,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "Happy Hour 5-7PM • 50% Off All Items",
              visualElement: "🎉",
              customVisual: "★★★ SPECIAL OFFER ★★★",
              picture: null
            },
            {
              text: "Weekend Special • Live DJ Saturday",
              visualElement: "🎵",
              customVisual: "♪♫♪ LIVE MUSIC ♪♫♪",
              picture: null
            },
            {
              text: "Group Discounts Available • Call Now",
              visualElement: "👥",
              customVisual: "═══ GROUP DEALS ═══",
              picture: null
            }
          ]
        },
        3: {
          name: "VIP & Premium Services",
          sequenceCount: 2,
          height: 100,
          backgroundColor: "#000000",
          textColor: "#ffffff",
          fontSize: 70,
          scrollSpeed: 45,
          sequences: [
            {
              text: "VIP Lounge Available • Private Rooms",
              visualElement: "💎",
              customVisual: "◆◇◆ LUXURY EXPERIENCE ◆◇◆",
              picture: null
            },
            {
              text: "Premium Flavors • Exclusive Blends",
              visualElement: "🏆",
              customVisual: "▶ PREMIUM QUALITY ◀",
              picture: null
            }
          ]
        }
      };
      saveTemplatesToStorage();
    }

    // Save templates to localStorage (simplified)
    function saveTemplatesToStorage() {
      try {
        const templatesJson = JSON.stringify(templates);
        localStorage.setItem('bannerProTemplates', templatesJson);
        console.log('Templates saved to localStorage');
        return true;
      } catch (error) {
        console.error('Error saving templates:', error);
        return false;
      }
    }

    // Load a template
    function loadTemplate(templateNum) {
      const template = templates[templateNum];
      if (!template) {
        showTemplateStatus(`Template ${templateNum} not found`, 'error');
        return;
      }

      try {
        // Load basic settings
        document.getElementById('sequenceCount').value = template.sequenceCount;
        document.getElementById('height').value = template.height;
        document.getElementById('backgroundColor').value = template.backgroundColor;
        document.getElementById('textColor').value = template.textColor;
        document.getElementById('fontSize').value = template.fontSize;
        document.getElementById('scrollSpeed').value = template.scrollSpeed;

        // Update sequence inputs visibility
        updateSequenceInputs();

        // Load sequences
        template.sequences.forEach((seq, index) => {
          const seqNum = index + 1;
          if (seqNum <= template.sequenceCount) {
            document.getElementById(`text${seqNum}`).value = seq.text || '';
            document.getElementById(`visualElement${seqNum}`).value = seq.visualElement || '';
            document.getElementById(`customVisual${seqNum}`).value = seq.customVisual || '';

            // Clear picture preview
            document.getElementById(`picturePreview${seqNum}`).innerHTML = 'No picture uploaded';
            sequences[index].picture = seq.picture;
          }
        });

        showTemplateStatus(`Template ${templateNum} loaded: ${template.name}`, 'success');
      } catch (error) {
        showTemplateStatus(`Error loading template ${templateNum}`, 'error');
        console.error('Error loading template:', error);
      }
    }

    // Save current settings as template
    async function saveTemplate(templateNum) {
      try {
        console.log(`Attempting to save template ${templateNum}`);

        // Collect current settings
        const sequenceCount = parseInt(document.getElementById('sequenceCount').value);
        console.log('Sequence count:', sequenceCount);

        const currentSequences = [];

        for (let i = 1; i <= sequenceCount; i++) {
          const sequence = {
            text: document.getElementById(`text${i}`) ? document.getElementById(`text${i}`).value || '' : '',
            visualElement: document.getElementById(`visualElement${i}`) ? document.getElementById(`visualElement${i}`).value || '' : '',
            customVisual: document.getElementById(`customVisual${i}`) ? document.getElementById(`customVisual${i}`).value || '' : '',
            picture: sequences[i - 1] ? sequences[i - 1].picture : null
          };
          currentSequences.push(sequence);
          console.log(`Sequence ${i}:`, sequence);
        }

        // Create custom input dialog since prompt() is not supported in Electron
        const templateName = await showInputDialog(`Enter name for Template ${templateNum}:`, templates[templateNum]?.name || `Template ${templateNum}`);
        if (!templateName) {
          console.log('User cancelled template save');
          return; // User cancelled
        }

        const newTemplate = {
          name: templateName,
          sequenceCount: sequenceCount,
          height: parseInt(document.getElementById('height').value) || 100,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 45,
          sequences: currentSequences
        };

        console.log('New template:', newTemplate);

        templates[templateNum] = newTemplate;

        saveTemplatesToStorage();
        showTemplateStatus(`Template ${templateNum} saved: ${templateName}`, 'success');
        console.log(`Template ${templateNum} saved successfully`);
      } catch (error) {
        showTemplateStatus(`Error saving template ${templateNum}: ${error.message}`, 'error');
        console.error('Error saving template:', error);
      }
    }

    // Custom input dialog (since prompt() is not supported in Electron)
    function showInputDialog(message, defaultValue = '') {
      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      `;

      // Create dialog box
      const dialog = document.createElement('div');
      dialog.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        min-width: 300px;
        max-width: 500px;
      `;

      dialog.innerHTML = `
        <h3 style="margin-top: 0; color: #333;">${message}</h3>
        <input type="text" id="templateNameInput" value="${defaultValue}" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; margin: 10px 0;">
        <div style="text-align: right; margin-top: 15px;">
          <button id="cancelBtn" style="padding: 8px 15px; margin-right: 10px; background: #ccc; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
          <button id="okBtn" style="padding: 8px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Save</button>
        </div>
      `;

      overlay.appendChild(dialog);
      document.body.appendChild(overlay);

      // Focus on input
      const input = document.getElementById('templateNameInput');
      input.focus();
      input.select();

      // Return promise that resolves with the input value or null if cancelled
      return new Promise((resolve) => {
        const cleanup = () => {
          document.body.removeChild(overlay);
        };

        document.getElementById('okBtn').onclick = () => {
          const value = input.value.trim();
          cleanup();
          resolve(value || null);
        };

        document.getElementById('cancelBtn').onclick = () => {
          cleanup();
          resolve(null);
        };

        // Handle Enter key
        input.onkeydown = (e) => {
          if (e.key === 'Enter') {
            const value = input.value.trim();
            cleanup();
            resolve(value || null);
          } else if (e.key === 'Escape') {
            cleanup();
            resolve(null);
          }
        };

        // Handle clicking outside dialog
        overlay.onclick = (e) => {
          if (e.target === overlay) {
            cleanup();
            resolve(null);
          }
        };
      });
    }

    // Show template status message
    function showTemplateStatus(message, type) {
      const statusDiv = document.getElementById('templateStatus');
      statusDiv.textContent = message;

      if (type === 'success') {
        statusDiv.style.color = '#4CAF50';
      } else if (type === 'error') {
        statusDiv.style.color = '#f44336';
      } else if (type === 'warning') {
        statusDiv.style.color = '#FF9800';
      } else {
        statusDiv.style.color = '#666';
      }

      // Reset to default after 5 seconds (longer for error messages)
      setTimeout(() => {
        statusDiv.textContent = 'Templates: Save your current settings or load previously saved configurations';
        statusDiv.style.color = '#666';
      }, 5000);
    }

    // Initialize templates on page load
    loadTemplatesFromStorage();

    function applySettings() {
      try {
        // Collect data from active sequences
        const sequenceCount = parseInt(document.getElementById('sequenceCount').value);
        const activeSequences = [];

        for (let i = 1; i <= sequenceCount; i++) {
          const sequence = {
            text: document.getElementById(`text${i}`).value || '',
            visualElement: document.getElementById(`visualElement${i}`).value || '',
            customVisual: document.getElementById(`customVisual${i}`).value.trim() || '',
            picture: sequences[i - 1].picture
          };

          // Only add sequence if it has at least text
          if (sequence.text) {
            activeSequences.push(sequence);
          }
        }

        const settings = {
          height: parseInt(document.getElementById('height').value) || 75,
          backgroundColor: document.getElementById('backgroundColor').value || '#000000',
          textColor: document.getElementById('textColor').value || '#ffffff',
          fontSize: parseInt(document.getElementById('fontSize').value) || 70,
          scrollSpeed: parseInt(document.getElementById('scrollSpeed').value) || 20,
          sequences: activeSequences
        };

        window.electronAPI.updateSettings(settings);

        // Visual feedback
        const button = document.querySelector('button');
        const originalText = button.textContent;
        button.textContent = 'Applied!';
        button.style.backgroundColor = '#4CAF50';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 1000);
      } catch (error) {
        console.error('Error applying settings:', error);
        alert('Error applying settings. Please try again.');
      }
    }

    // Banner visibility functions
    function toggleBanner() {
      window.electronAPI.toggleBanner();
    }

    function showBanner() {
      window.electronAPI.showBanner();
    }

    function hideBanner() {
      window.electronAPI.hideBanner();
    }

    // Listen for banner visibility changes
    window.electronAPI.onBannerVisibilityChanged((event, isVisible) => {
      bannerVisible = isVisible;
      const statusElement = document.getElementById('bannerStatus');
      const toggleBtn = document.getElementById('toggleBannerBtn');

      if (isVisible) {
        statusElement.textContent = 'Banner: Visible';
        statusElement.style.color = '#4CAF50';
        toggleBtn.textContent = '🙈 Hide Banner';
      } else {
        statusElement.textContent = 'Banner: Hidden';
        statusElement.style.color = '#f44336';
        toggleBtn.textContent = '👁️ Show Banner';
      }
    });

    // Initialize banner status on load
    window.addEventListener('load', () => {
      // Banner starts visible by default
      const statusElement = document.getElementById('bannerStatus');
      const toggleBtn = document.getElementById('toggleBannerBtn');
      statusElement.textContent = 'Banner: Visible';
      statusElement.style.color = '#4CAF50';
      toggleBtn.textContent = '🙈 Hide Banner';
    });

    // Add keyboard shortcut (Ctrl+B) to toggle banner
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        toggleBanner();
      }
    });
  </script>
</body>
</html>