const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // For the control panel
  updateSettings: (settings) => ipcRenderer.send('update-settings', settings),

  // Banner visibility controls
  toggleBanner: () => ipcRenderer.send('toggle-banner'),
  showBanner: () => ipcRenderer.send('show-banner'),
  hideBanner: () => ipcRenderer.send('hide-banner'),
  onBannerVisibilityChanged: (callback) => ipcRenderer.on('banner-visibility-changed', callback),

  // License system functions
  checkLicense: () => ipcRenderer.invoke('check-license'),
  activateLicense: (licenseKey, customerInfo) => ipcRenderer.invoke('activate-license', licenseKey, customerInfo),
  getTrialStatus: () => ipcRenderer.invoke('get-trial-status'),
  startTrial: () => ipcRenderer.invoke('start-trial'),
  getHardwareFingerprint: () => ipcRenderer.invoke('get-hardware-fingerprint'),
  startMainApp: () => ipcRenderer.send('start-main-app'),

  // For the banner window
  onApplySettings: (callback) => ipcRenderer.on('apply-settings', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // File system operations (for image loading)
  readFile: (filePath) => {
    const fs = require('fs');
    try {
      if (!filePath || typeof filePath !== 'string') {
        throw new Error('Invalid file path provided');
      }
      return fs.readFileSync(filePath);
    } catch (error) {
      console.error('Error reading file:', error.message);
      return null;
    }
  },
  
  // Path operations
  join: (...paths) => {
    const path = require('path');
    return path.join(...paths);
  },
  
  // Check if file exists
  fileExists: (filePath) => {
    const fs = require('fs');
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      return false;
    }
  }
});
