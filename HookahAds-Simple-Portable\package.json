{"name": "banner-pro", "version": "1.0.0", "description": "Professional advertisement banner and digital signage solution for businesses, events, and organizations", "main": "main.js", "author": "Banner Pro <<EMAIL>>", "license": "Commercial", "homepage": "https://bannerpro.com", "repository": {"type": "git", "url": "https://github.com/bannerpro/banner-pro.git"}, "keywords": ["digital signage", "advertisement", "banner", "overlay", "business", "marketing", "display", "electron"], "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-portable": "electron-builder --win portable", "build-installer": "electron-builder --win nsis", "build-all": "electron-builder --win --linux --mac", "dist": "npm run build-portable && npm run build-installer", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "npm run clean && npm run dist"}, "build": {"appId": "com.bannerpro.app", "productName": "Banner Pro", "copyright": "Copyright © 2024 Banner Pro. All rights reserved.", "directories": {"output": "dist"}, "files": ["main.js", "banner.html", "control.html", "license-activation.html", "license-system.js", "preload.js", "package.json", "LICENSE.txt", "EULA.txt", "HOW-TO-RUN.txt", "START-BANNER-PRO.bat", "START-BANNER-PRO-NO-TERMINAL.bat", "START-BANNER-PRO-HIDDEN.vbs"], "compression": "normal", "nsis": {"artifactName": "BannerPro-Setup.exe", "oneClick": false, "allowToChangeInstallationDirectory": true}, "win": {"target": [{"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "portable": {"artifactName": "BannerPro-Portable.exe"}}, "devDependencies": {"electron": "^24.3.0", "electron-builder": "^24.13.3", "rimraf": "^5.0.0"}}