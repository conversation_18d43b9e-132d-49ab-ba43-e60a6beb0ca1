#!/usr/bin/env node

/**
 * Banner Pro License Key Generator
 * 
 * This tool generates license keys for Banner Pro software.
 * Usage: node license-generator.js [options]
 */

const LicenseSystem = require('./license-system');
const fs = require('fs');
const path = require('path');

class LicenseGenerator {
  constructor() {
    this.licenseSystem = new LicenseSystem();
    this.outputFile = 'generated-licenses.txt';
  }

  // Generate a single license key
  generateSingleLicense(customerInfo = {}) {
    const result = this.licenseSystem.generateLicenseKey(customerInfo);
    return {
      licenseKey: result.licenseKey,
      customerInfo: customerInfo,
      generatedAt: new Date().toISOString(),
      licenseData: result.licenseData
    };
  }

  // Generate multiple license keys
  generateBatchLicenses(count, customerInfoArray = []) {
    const licenses = [];
    
    for (let i = 0; i < count; i++) {
      const customerInfo = customerInfoArray[i] || {
        name: `Customer ${i + 1}`,
        email: `customer${i + 1}@example.com`,
        company: `Company ${i + 1}`
      };
      
      licenses.push(this.generateSingleLicense(customerInfo));
    }
    
    return licenses;
  }

  // Save licenses to file
  saveLicensesToFile(licenses, filename = null) {
    const outputPath = filename || this.outputFile;
    let content = `Banner Pro License Keys\nGenerated: ${new Date().toISOString()}\n`;
    content += '='.repeat(50) + '\n\n';

    licenses.forEach((license, index) => {
      content += `License #${index + 1}\n`;
      content += `Key: ${license.licenseKey}\n`;
      content += `Customer: ${license.customerInfo.name || 'N/A'}\n`;
      content += `Email: ${license.customerInfo.email || 'N/A'}\n`;
      content += `Company: ${license.customerInfo.company || 'N/A'}\n`;
      content += `Generated: ${license.generatedAt}\n`;
      content += '-'.repeat(30) + '\n\n';
    });

    fs.writeFileSync(outputPath, content);
    console.log(`✅ Licenses saved to: ${outputPath}`);
  }

  // Save licenses as CSV
  saveLicensesAsCSV(licenses, filename = 'licenses.csv') {
    let csvContent = 'License Key,Customer Name,Email,Company,Generated Date\n';
    
    licenses.forEach(license => {
      csvContent += `"${license.licenseKey}","${license.customerInfo.name || ''}","${license.customerInfo.email || ''}","${license.customerInfo.company || ''}","${license.generatedAt}"\n`;
    });

    fs.writeFileSync(filename, csvContent);
    console.log(`✅ CSV saved to: ${filename}`);
  }

  // Interactive CLI
  async runInteractive() {
    console.log('🚀 Banner Pro License Generator');
    console.log('================================\n');

    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const question = (prompt) => new Promise(resolve => rl.question(prompt, resolve));

    try {
      const choice = await question('Choose option:\n1. Generate single license\n2. Generate batch licenses\n3. Exit\nEnter choice (1-3): ');

      switch (choice) {
        case '1':
          await this.generateSingleInteractive(rl, question);
          break;
        case '2':
          await this.generateBatchInteractive(rl, question);
          break;
        case '3':
          console.log('Goodbye!');
          break;
        default:
          console.log('Invalid choice');
      }
    } catch (error) {
      console.error('Error:', error.message);
    } finally {
      rl.close();
    }
  }

  async generateSingleInteractive(rl, question) {
    console.log('\n📝 Single License Generation');
    console.log('-----------------------------');

    const name = await question('Customer Name: ');
    const email = await question('Customer Email: ');
    const company = await question('Company Name: ');

    const customerInfo = { name, email, company };
    const license = this.generateSingleLicense(customerInfo);

    console.log('\n✅ License Generated:');
    console.log(`🔑 License Key: ${license.licenseKey}`);
    console.log(`👤 Customer: ${name}`);
    console.log(`📧 Email: ${email}`);
    console.log(`🏢 Company: ${company}`);

    const save = await question('\nSave to file? (y/n): ');
    if (save.toLowerCase() === 'y') {
      this.saveLicensesToFile([license]);
    }
  }

  async generateBatchInteractive(rl, question) {
    console.log('\n📦 Batch License Generation');
    console.log('----------------------------');

    const countStr = await question('Number of licenses to generate: ');
    const count = parseInt(countStr);

    if (isNaN(count) || count <= 0) {
      console.log('Invalid number');
      return;
    }

    console.log(`\nGenerating ${count} licenses...`);
    const licenses = this.generateBatchLicenses(count);

    console.log('\n✅ Licenses Generated:');
    licenses.forEach((license, index) => {
      console.log(`${index + 1}. ${license.licenseKey}`);
    });

    const saveFormat = await question('\nSave format:\n1. Text file\n2. CSV file\n3. Both\nChoose (1-3): ');

    switch (saveFormat) {
      case '1':
        this.saveLicensesToFile(licenses);
        break;
      case '2':
        this.saveLicensesAsCSV(licenses);
        break;
      case '3':
        this.saveLicensesToFile(licenses);
        this.saveLicensesAsCSV(licenses);
        break;
      default:
        console.log('No files saved');
    }
  }

  // Command line interface
  runCLI() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
      this.showHelp();
      return;
    }

    const command = args[0];

    switch (command) {
      case 'generate':
        this.handleGenerateCommand(args.slice(1));
        break;
      case 'batch':
        this.handleBatchCommand(args.slice(1));
        break;
      case 'interactive':
        this.runInteractive();
        break;
      default:
        console.log(`Unknown command: ${command}`);
        this.showHelp();
    }
  }

  handleGenerateCommand(args) {
    const customerInfo = {
      name: this.getArgValue(args, '--name') || 'Default Customer',
      email: this.getArgValue(args, '--email') || '<EMAIL>',
      company: this.getArgValue(args, '--company') || 'Default Company'
    };

    const license = this.generateSingleLicense(customerInfo);
    console.log(`Generated License Key: ${license.licenseKey}`);

    if (args.includes('--save')) {
      this.saveLicensesToFile([license]);
    }
  }

  handleBatchCommand(args) {
    const count = parseInt(this.getArgValue(args, '--count')) || 10;
    const licenses = this.generateBatchLicenses(count);
    
    console.log(`Generated ${count} license keys:`);
    licenses.forEach((license, index) => {
      console.log(`${index + 1}. ${license.licenseKey}`);
    });

    if (args.includes('--save')) {
      this.saveLicensesToFile(licenses);
    }

    if (args.includes('--csv')) {
      this.saveLicensesAsCSV(licenses);
    }
  }

  getArgValue(args, flag) {
    const index = args.indexOf(flag);
    return index !== -1 && index + 1 < args.length ? args[index + 1] : null;
  }

  showHelp() {
    console.log(`
🚀 Banner Pro License Generator

Usage:
  node license-generator.js <command> [options]

Commands:
  generate              Generate a single license key
  batch                 Generate multiple license keys
  interactive           Run in interactive mode

Options for 'generate':
  --name <name>         Customer name
  --email <email>       Customer email
  --company <company>   Company name
  --save                Save to file

Options for 'batch':
  --count <number>      Number of licenses (default: 10)
  --save                Save to text file
  --csv                 Save as CSV file

Examples:
  node license-generator.js interactive
  node license-generator.js generate --name "John Doe" --email "<EMAIL>" --save
  node license-generator.js batch --count 50 --save --csv
`);
  }
}

// Run if called directly
if (require.main === module) {
  const generator = new LicenseGenerator();
  generator.runCLI();
}

module.exports = LicenseGenerator;
