{"version": 3, "file": "SnapOptions.js", "sourceRoot": "", "sources": ["../../src/options/SnapOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../core\"\nimport { CommonLinuxOptions } from \"./linuxOptions\"\n\nexport interface SnapOptions extends CommonLinuxOptions, TargetSpecificOptions {\n  /**\n   * A snap of type base to be used as the execution environment for this snap. Examples: `core`, `core18`, `core20`, `core22`. Defaults to `core20`\n   */\n  readonly base?: string | null\n\n  /**\n   * The type of [confinement](https://snapcraft.io/docs/reference/confinement) supported by the snap.\n   * @default strict\n   */\n  readonly confinement?: \"devmode\" | \"strict\" | \"classic\" | null\n\n  /**\n   * The custom environment. Defaults to `{\"TMPDIR: \"$XDG_RUNTIME_DIR\"}`. If you set custom, it will be merged with default.\n   */\n  readonly environment?: { [key: string]: string } | null\n\n  /**\n   * The 78 character long summary. Defaults to [productName](/configuration/configuration#Configuration-productName).\n   */\n  readonly summary?: string | null\n\n  /**\n   * The quality grade of the snap. It can be either `devel` (i.e. a development version of the snap, so not to be published to the “stable” or “candidate” channels) or “stable” (i.e. a stable release or release candidate, which can be released to all channels).\n   * @default stable\n   */\n  readonly grade?: \"devel\" | \"stable\" | null\n\n  /**\n   * The list of features that must be supported by the core in order for this snap to install.\n   */\n  readonly assumes?: Array<string> | string | null\n\n  /**\n   * The list of debian packages needs to be installed for building this snap.\n   */\n  readonly buildPackages?: Array<string> | null\n\n  /**\n   * The list of Ubuntu packages to use that are needed to support the `app` part creation. Like `depends` for `deb`.\n   * Defaults to `[\"libnspr4\", \"libnss3\", \"libxss1\", \"libappindicator3-1\", \"libsecret-1-0\"]`.\n   *\n   * If list contains `default`, it will be replaced to default list, so, `[\"default\", \"foo\"]` can be used to add custom package `foo` in addition to defaults.\n   */\n  readonly stagePackages?: Array<string> | null\n\n  /**\n   * The [hooks](https://docs.snapcraft.io/build-snaps/hooks) directory, relative to `build` (build resources directory).\n   * @default build/snap-hooks\n   */\n  readonly hooks?: string | null\n\n  /**\n   * The list of [plugs](https://snapcraft.io/docs/reference/interfaces).\n   * Defaults to `[\"desktop\", \"desktop-legacy\", \"home\", \"x11\", \"wayland\", \"unity7\", \"browser-support\", \"network\", \"gsettings\", \"audio-playback\", \"pulseaudio\", \"opengl\"]`.\n   *\n   * If list contains `default`, it will be replaced to default list, so, `[\"default\", \"foo\"]` can be used to add custom plug `foo` in addition to defaults.\n   *\n   * Additional attributes can be specified using object instead of just name of plug:\n   * ```\n   *[\n   *  {\n   *    \"browser-sandbox\": {\n   *      \"interface\": \"browser-support\",\n   *      \"allow-sandbox\": true\n   *    },\n   *  },\n   *  \"another-simple-plug-name\"\n   *]\n   * ```\n   */\n  readonly plugs?: Array<string | PlugDescriptor> | PlugDescriptor | null\n\n  /**\n   * The list of [slots](https://snapcraft.io/docs/reference/interfaces).\n   *\n   * Additional attributes can be specified using object instead of just name of slot:\n   * ```\n   *[\n   *  {\n   *    \"mpris\": {\n   *      \"name\": \"chromium\"\n   *    },\n   *  }\n   *]\n   *\n   * In case you want your application to be a compliant MPris player, you will need to definie\n   * The mpris slot with \"chromium\" name.\n   * This electron has it [hardcoded](https://source.chromium.org/chromium/chromium/src/+/master:components/system_media_controls/linux/system_media_controls_linux.cc;l=51;bpv=0;bpt=1),\n   * and we need to pass this name so snap [will allow it](https://forum.snapcraft.io/t/unable-to-use-mpris-interface/15360/7) in strict confinement.\n   *\n   */\n  readonly slots?: Array<string | SlotDescriptor> | PlugDescriptor | null\n\n  /**\n   * Specifies any [parts](https://snapcraft.io/docs/reference/parts) that should be built before this part.\n   * Defaults to `[\"desktop-gtk2\"\"]`.\n   *\n   * If list contains `default`, it will be replaced to default list, so, `[\"default\", \"foo\"]` can be used to add custom parts `foo` in addition to defaults.\n   */\n  readonly after?: Array<string> | null\n\n  /**\n   * Whether to use template snap. Defaults to `true` if `stagePackages` not specified.\n   */\n  readonly useTemplateApp?: boolean\n\n  /**\n   * Whether or not the snap should automatically start on login.\n   * @default false\n   */\n  readonly autoStart?: boolean\n\n  /**\n   * Specifies any files to make accessible from locations such as `/usr`, `/var`, and `/etc`. See [snap layouts](https://snapcraft.io/docs/snap-layouts) to learn more.\n   */\n  readonly layout?: { [key: string]: { [key: string]: string } } | null\n\n  /**\n   * Specifies which files from the app part to stage and which to exclude. Individual files, directories, wildcards, globstars, and exclusions are accepted. See [Snapcraft filesets](https://snapcraft.io/docs/snapcraft-filesets) to learn more about the format.\n   *\n   * The defaults can be found in [snap.ts](https://github.com/electron-userland/electron-builder/blob/master/packages/app-builder-lib/templates/snap/snapcraft.yaml#L29).\n   */\n  readonly appPartStage?: Array<string> | null\n\n  /**\n   * An optional title for the snap, may contain uppercase letters and spaces. Defaults to `productName`. See [snap format documentation](https://snapcraft.io/docs/snap-format).\n   */\n  readonly title?: string | null\n\n  /**\n   * Sets the compression type for the snap. Can be xz, lzo, or null.\n   */\n  readonly compression?: \"xz\" | \"lzo\" | null\n\n  /**\n   * Allow running the program with native wayland support with --ozone-platform=wayland.\n   * Disabled by default because of this issue in older Electron/Snap versions: https://github.com/electron-userland/electron-builder/issues/4007\n   */\n  readonly allowNativeWayland?: boolean | null\n}\n\nexport interface PlugDescriptor {\n  [key: string]: { [key: string]: any } | null\n}\n\nexport interface SlotDescriptor {\n  [key: string]: { [key: string]: any } | null\n}\n"]}