const crypto = require('crypto');
const os = require('os');
const fs = require('fs');
const path = require('path');

class LicenseSystem {
  constructor() {
    this.licenseFile = path.join(os.homedir(), '.bannerpro', 'license.dat');
    this.secretKey = 'BannerPro2024SecretKey!@#$%'; // In production, use environment variable
    this.ensureLicenseDirectory();
  }

  ensureLicenseDirectory() {
    const dir = path.dirname(this.licenseFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  // Generate hardware fingerprint
  generateHardwareFingerprint() {
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    
    // Create a unique identifier based on hardware
    const hwInfo = {
      platform: os.platform(),
      arch: os.arch(),
      cpuModel: cpus[0]?.model || 'unknown',
      cpuCount: cpus.length,
      totalMemory: os.totalmem(),
      hostname: os.hostname()
    };

    // Get MAC addresses
    const macAddresses = [];
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      }
    }
    hwInfo.macAddresses = macAddresses.sort();

    // Create hash of hardware info
    const hwString = JSON.stringify(hwInfo);
    return crypto.createHash('sha256').update(hwString).digest('hex').substring(0, 16);
  }

  // Generate license key format: XXXX-XXXX-XXXX-XXXX
  generateLicenseKey(customerInfo = {}) {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');
    const customerHash = crypto.createHash('md5')
      .update(JSON.stringify(customerInfo))
      .digest('hex')
      .substring(0, 8);

    // Create license data
    const licenseData = {
      timestamp,
      customer: customerInfo,
      version: '1.0.0',
      type: 'standard'
    };

    // Create checksum
    const dataString = JSON.stringify(licenseData);
    const checksum = crypto.createHmac('sha256', this.secretKey)
      .update(dataString)
      .digest('hex')
      .substring(0, 8);

    // Format as XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      customerHash.substring(0, 4).toUpperCase(),
      random.substring(0, 4).toUpperCase(),
      timestamp.toString(36).substring(-4).toUpperCase(),
      checksum.substring(0, 4).toUpperCase()
    ];

    return {
      licenseKey: keyParts.join('-'),
      licenseData: licenseData
    };
  }

  // Validate license key format and checksum
  validateLicenseKeyFormat(licenseKey) {
    // Check format XXXX-XXXX-XXXX-XXXX
    const keyPattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
    return keyPattern.test(licenseKey);
  }

  // Activate license (bind to hardware)
  async activateLicense(licenseKey, customerInfo = {}) {
    try {
      if (!this.validateLicenseKeyFormat(licenseKey)) {
        return { success: false, error: 'Invalid license key format' };
      }

      // Check if already activated
      if (this.isLicenseActivated()) {
        const existingLicense = this.getLicenseInfo();
        if (existingLicense.licenseKey === licenseKey) {
          return { success: true, message: 'License already activated on this machine' };
        } else {
          return { success: false, error: 'Different license already activated on this machine' };
        }
      }

      // Generate hardware fingerprint
      const hardwareFingerprint = this.generateHardwareFingerprint();

      // Create activation record
      const activationData = {
        licenseKey: licenseKey,
        hardwareFingerprint: hardwareFingerprint,
        activatedAt: new Date().toISOString(),
        customerInfo: customerInfo,
        version: '1.0.0'
      };

      // Encrypt and save license
      const encryptedData = this.encryptLicenseData(activationData);
      fs.writeFileSync(this.licenseFile, encryptedData);

      return { success: true, message: 'License activated successfully' };

    } catch (error) {
      return { success: false, error: 'Activation failed: ' + error.message };
    }
  }

  // Check if license is activated and valid
  isLicenseActivated() {
    try {
      if (!fs.existsSync(this.licenseFile)) {
        return false;
      }

      const licenseData = this.getLicenseInfo();
      if (!licenseData) {
        return false;
      }

      // Verify hardware fingerprint
      const currentFingerprint = this.generateHardwareFingerprint();
      if (licenseData.hardwareFingerprint !== currentFingerprint) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  // Get license information
  getLicenseInfo() {
    try {
      if (!fs.existsSync(this.licenseFile)) {
        return null;
      }

      const encryptedData = fs.readFileSync(this.licenseFile, 'utf8');
      return this.decryptLicenseData(encryptedData);
    } catch (error) {
      return null;
    }
  }

  // Encrypt license data
  encryptLicenseData(data) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.createHash('sha256').update(this.secretKey).digest();
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  }

  // Decrypt license data
  decryptLicenseData(encryptedData) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.createHash('sha256').update(this.secretKey).digest();

    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  }

  // Remove license (for testing or deactivation)
  removeLicense() {
    try {
      if (fs.existsSync(this.licenseFile)) {
        fs.unlinkSync(this.licenseFile);
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get trial status (30-day trial)
  getTrialStatus() {
    const trialFile = path.join(path.dirname(this.licenseFile), 'trial.dat');
    
    try {
      if (!fs.existsSync(trialFile)) {
        // Start trial
        const trialData = {
          startDate: new Date().toISOString(),
          hardwareFingerprint: this.generateHardwareFingerprint()
        };
        fs.writeFileSync(trialFile, this.encryptLicenseData(trialData));
        return { isTrialActive: true, daysRemaining: 30 };
      }

      const encryptedData = fs.readFileSync(trialFile, 'utf8');
      const trialData = this.decryptLicenseData(encryptedData);
      
      // Verify hardware fingerprint
      if (trialData.hardwareFingerprint !== this.generateHardwareFingerprint()) {
        return { isTrialActive: false, daysRemaining: 0 };
      }

      const startDate = new Date(trialData.startDate);
      const currentDate = new Date();
      const daysPassed = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
      const daysRemaining = Math.max(0, 30 - daysPassed);

      return {
        isTrialActive: daysRemaining > 0,
        daysRemaining: daysRemaining,
        startDate: trialData.startDate
      };

    } catch (error) {
      return { isTrialActive: false, daysRemaining: 0 };
    }
  }
}

module.exports = LicenseSystem;
