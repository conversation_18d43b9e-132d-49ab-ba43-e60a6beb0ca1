<!DOCTYPE html>
<html>
<head>
  <title>Banner Pro - License Activation</title>
  <style>
    * {
      box-sizing: border-box;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .activation-container {
      background: rgba(255, 255, 255, 0.95);
      padding: 40px;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-width: 500px;
      width: 90%;
      text-align: center;
    }
    .logo {
      font-size: 32px;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 10px;
    }
    .subtitle {
      color: #666;
      margin-bottom: 30px;
      font-size: 16px;
    }
    .license-input-group {
      margin-bottom: 20px;
    }
    .license-input {
      width: 100%;
      padding: 15px;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 18px;
      text-align: center;
      font-family: 'Courier New', monospace;
      letter-spacing: 2px;
      text-transform: uppercase;
      transition: all 0.3s ease;
    }
    .license-input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .license-input::placeholder {
      color: #999;
      letter-spacing: 1px;
    }
    .customer-info {
      margin-bottom: 20px;
      text-align: left;
    }
    .customer-info input {
      width: 100%;
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      margin-bottom: 10px;
      font-size: 14px;
    }
    .customer-info input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .button-group {
      margin-top: 30px;
    }
    .btn {
      padding: 15px 30px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin: 5px;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    .btn-secondary {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
      color: white;
    }
    .btn-secondary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(149, 165, 166, 0.3);
    }
    .trial-info {
      background: rgba(255, 193, 7, 0.1);
      border: 2px solid #ffc107;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
      color: #856404;
    }
    .trial-info.expired {
      background: rgba(220, 53, 69, 0.1);
      border-color: #dc3545;
      color: #721c24;
    }
    .status-message {
      margin-top: 20px;
      padding: 15px;
      border-radius: 10px;
      font-weight: 500;
    }
    .status-success {
      background: rgba(40, 167, 69, 0.1);
      border: 2px solid #28a745;
      color: #155724;
    }
    .status-error {
      background: rgba(220, 53, 69, 0.1);
      border: 2px solid #dc3545;
      color: #721c24;
    }
    .license-format {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    .hardware-info {
      font-size: 11px;
      color: #999;
      margin-top: 20px;
      padding: 10px;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 5px;
    }
  </style>
</head>
<body>
  <div class="activation-container">
    <div class="logo">🚀 Banner Pro</div>
    <div class="subtitle">Professional Digital Signage Solution</div>
    
    <div id="trialInfo" class="trial-info" style="display: none;">
      <strong>Trial Version</strong><br>
      <span id="trialStatus"></span>
    </div>

    <div class="license-input-group">
      <input type="text" id="licenseKey" class="license-input" placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19">
      <div class="license-format">Enter your license key in the format: XXXX-XXXX-XXXX-XXXX</div>
    </div>

    <div class="customer-info">
      <input type="text" id="customerName" placeholder="Your Name (Optional)">
      <input type="email" id="customerEmail" placeholder="Your Email (Optional)">
      <input type="text" id="customerCompany" placeholder="Company Name (Optional)">
    </div>

    <div class="button-group">
      <button class="btn btn-primary" onclick="activateLicense()">🔑 Activate License</button>
      <button class="btn btn-secondary" onclick="startTrial()" id="trialBtn">⏱️ Start Trial</button>
      <button class="btn btn-secondary" onclick="continueTrial()" id="continueBtn" style="display: none;">▶️ Continue</button>
    </div>

    <div id="statusMessage" class="status-message" style="display: none;"></div>

    <div class="hardware-info">
      <strong>Hardware ID:</strong> <span id="hardwareId">Loading...</span><br>
      <small>This license will be bound to this specific computer</small>
    </div>
  </div>

  <script>
    let hardwareFingerprint = '';

    // Format license key input
    document.getElementById('licenseKey').addEventListener('input', function(e) {
      let value = e.target.value.replace(/[^A-Z0-9]/g, '').toUpperCase();
      let formatted = value.match(/.{1,4}/g)?.join('-') || value;
      if (formatted.length > 19) formatted = formatted.substring(0, 19);
      e.target.value = formatted;
    });

    // Initialize on load
    window.addEventListener('load', async () => {
      await checkLicenseStatus();
      await loadTrialStatus();
      await getHardwareFingerprint();
    });

    async function checkLicenseStatus() {
      try {
        const result = await window.electronAPI.checkLicense();
        if (result.isActivated) {
          showStatus('License already activated! Starting application...', 'success');
          setTimeout(() => {
            window.electronAPI.startMainApp();
          }, 2000);
        }
      } catch (error) {
        console.error('Error checking license:', error);
      }
    }

    async function loadTrialStatus() {
      try {
        const trialStatus = await window.electronAPI.getTrialStatus();
        const trialInfo = document.getElementById('trialInfo');
        const trialStatusSpan = document.getElementById('trialStatus');
        const trialBtn = document.getElementById('trialBtn');
        const continueBtn = document.getElementById('continueBtn');

        if (trialStatus.daysRemaining > 0) {
          trialInfo.style.display = 'block';
          trialStatusSpan.textContent = `${trialStatus.daysRemaining} days remaining`;
          trialBtn.style.display = 'none';
          continueBtn.style.display = 'inline-block';
        } else if (trialStatus.daysRemaining === 0) {
          trialInfo.style.display = 'block';
          trialInfo.classList.add('expired');
          trialStatusSpan.textContent = 'Trial expired - License required';
          trialBtn.style.display = 'none';
          continueBtn.style.display = 'none';
        }
      } catch (error) {
        console.error('Error loading trial status:', error);
      }
    }

    async function getHardwareFingerprint() {
      try {
        const fingerprint = await window.electronAPI.getHardwareFingerprint();
        hardwareFingerprint = fingerprint;
        document.getElementById('hardwareId').textContent = fingerprint;
      } catch (error) {
        document.getElementById('hardwareId').textContent = 'Error loading';
      }
    }

    async function activateLicense() {
      const licenseKey = document.getElementById('licenseKey').value.trim();
      const customerName = document.getElementById('customerName').value.trim();
      const customerEmail = document.getElementById('customerEmail').value.trim();
      const customerCompany = document.getElementById('customerCompany').value.trim();

      if (!licenseKey) {
        showStatus('Please enter a license key', 'error');
        return;
      }

      if (licenseKey.length !== 19) {
        showStatus('License key must be in format XXXX-XXXX-XXXX-XXXX', 'error');
        return;
      }

      const customerInfo = {
        name: customerName,
        email: customerEmail,
        company: customerCompany
      };

      try {
        showStatus('Activating license...', 'success');
        const result = await window.electronAPI.activateLicense(licenseKey, customerInfo);
        
        if (result.success) {
          showStatus('License activated successfully! Starting application...', 'success');
          setTimeout(() => {
            window.electronAPI.startMainApp();
          }, 2000);
        } else {
          showStatus('Activation failed: ' + result.error, 'error');
        }
      } catch (error) {
        showStatus('Activation error: ' + error.message, 'error');
      }
    }

    async function startTrial() {
      try {
        const result = await window.electronAPI.startTrial();
        if (result.success) {
          showStatus('Trial started! Starting application...', 'success');
          setTimeout(() => {
            window.electronAPI.startMainApp();
          }, 2000);
        } else {
          showStatus('Trial start failed: ' + result.error, 'error');
        }
      } catch (error) {
        showStatus('Trial error: ' + error.message, 'error');
      }
    }

    async function continueTrial() {
      window.electronAPI.startMainApp();
    }

    function showStatus(message, type) {
      const statusDiv = document.getElementById('statusMessage');
      statusDiv.textContent = message;
      statusDiv.className = `status-message status-${type}`;
      statusDiv.style.display = 'block';
    }
  </script>
</body>
</html>
