export function updateStatisticsBoth(entry1: any, entry2: any, same: any, reason: any, type: any, permissionDeniedState: any, statistics: any, options: any): void;
export function updateStatisticsBoth(entry1: any, entry2: any, same: any, reason: any, type: any, permissionDeniedState: any, statistics: any, options: any): void;
export function updateStatisticsLeft(entry1: any, type: any, permissionDeniedState: any, statistics: any, options: any): void;
export function updateStatisticsLeft(entry1: any, type: any, permissionDeniedState: any, statistics: any, options: any): void;
export function updateStatisticsRight(entry2: any, type: any, permissionDeniedState: any, statistics: any, options: any): void;
export function updateStatisticsRight(entry2: any, type: any, permissionDeniedState: any, statistics: any, options: any): void;
//# sourceMappingURL=statisticsUpdate.d.ts.map