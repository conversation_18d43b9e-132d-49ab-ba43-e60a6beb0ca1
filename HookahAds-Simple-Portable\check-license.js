const LicenseSystem = require('./license-system');
const path = require('path');
const os = require('os');
const fs = require('fs');

console.log('🔍 Checking License Status...\n');

const licenseSystem = new LicenseSystem();

// Check license file location
const licenseFile = path.join(os.homedir(), '.bannerpro', 'license.dat');
const trialFile = path.join(os.homedir(), '.bannerpro', 'trial.dat');

console.log('📁 License Files:');
console.log(`License file: ${licenseFile}`);
console.log(`License exists: ${fs.existsSync(licenseFile) ? '✅ Yes' : '❌ No'}`);
console.log(`Trial file: ${trialFile}`);
console.log(`Trial exists: ${fs.existsSync(trialFile) ? '✅ Yes' : '❌ No'}`);

// Check license status
const isActivated = licenseSystem.isLicenseActivated();
console.log(`\n🔑 License activated: ${isActivated ? '✅ Yes' : '❌ No'}`);

// Check trial status
const trialStatus = licenseSystem.getTrialStatus();
console.log(`\n⏱️ Trial Status:`);
console.log(`  Active: ${trialStatus.isTrialActive ? '✅ Yes' : '❌ No'}`);
console.log(`  Days remaining: ${trialStatus.daysRemaining}`);

// Show what should happen
console.log(`\n🚀 App should:`);
if (isActivated) {
    console.log('  ✅ Start normally (licensed)');
} else if (trialStatus.isTrialActive) {
    console.log('  ✅ Start normally (trial active)');
} else {
    console.log('  🔑 Show activation window');
}

console.log('\n💡 To reset and test activation:');
console.log('   Delete the .bannerpro folder and restart the app');
console.log(`   Folder location: ${path.join(os.homedir(), '.bannerpro')}`);
