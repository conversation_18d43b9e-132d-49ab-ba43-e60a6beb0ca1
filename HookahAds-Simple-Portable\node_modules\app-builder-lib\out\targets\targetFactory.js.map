{"version": 3, "file": "targetFactory.js", "sourceRoot": "", "sources": ["../../src/targets/targetFactory.ts"], "names": [], "mappings": ";;;AAAA,+CAAgF;AAChF,kCAA2F;AAE3F,mDAA+C;AAE/C,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAA;AAEtF,SAAgB,2BAA2B,CAAC,GAA6B,EAAE,gBAAuC,EAAE,QAAkB;IACpI,KAAK,MAAM,WAAW,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;QACvC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,oEAAoE;YACpE,OAAO,GAAG,CAAA;QACZ,CAAC;IACH,CAAC;IAED,MAAM,YAAY,GAAoB,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAI,CAAC,EAAE,CAAa,CAAC,CAAA;IAC1I,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;IAC3B,KAAK,MAAM,MAAM,IAAI,IAAA,sBAAO,EAAC,gBAAgB,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC,GAAG,CAAsB,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAClK,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAA;QACxB,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAA;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QACvC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;YAC5C,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAa,CAAA;YAC5D,CAAC;QACH,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAA,sBAAO,EAAC,KAAK,CAAC,EAAE,CAAC;YACjE,IAAA,uBAAQ,EAAC,MAAM,EAAE,IAAA,6BAAc,EAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;QAC9C,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QACtB,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAA;QACpD,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,eAAQ,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,EAAE,CAAC;YACrH,MAAM,CAAC,GAAG,CAAC,mBAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;YACnC,oGAAoG;YACpG,oCAAoC;QACtC,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,MAAM,CAAC,GAAG,CAAC,IAAA,6BAAc,EAAC,IAAI,CAAC,EAAE,aAAa,CAAC,CAAA;YACjD,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAxCD,kEAwCC;AAED,SAAgB,aAAa,CAAC,YAAiC,EAAE,OAAsB,EAAE,MAAc,EAAE,QAA+B;IACtI,MAAM,MAAM,GAAkB,EAAE,CAAA;IAEhC,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,OAAmC,EAAE,EAAE;QACnE,IAAI,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACnC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;YACxB,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QAChC,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACrB,CAAC,CAAA;IAED,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAA;IACjE,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IACvC,OAAO,MAAM,CAAA;AACf,CAAC;AAfD,sCAeC;AAED,SAAS,gBAAgB,CAAC,OAAsB,EAAE,aAA4B;IAC5E,MAAM,IAAI,GAAkB,EAAE,CAAA;IAC9B,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAA;QACnC,IAAI,IAAI,KAAK,qBAAc,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAA;QAC7B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAgB,kBAAkB,CAAC,MAAc,EAAE,MAAc,EAAE,QAA+B;IAChG,IAAI,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAI,6BAAa,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;IACpD,CAAC;SAAM,IAAI,MAAM,KAAK,iBAAU,EAAE,CAAC;QACjC,OAAO,IAAI,UAAU,CAAC,iBAAU,CAAC,CAAA;IACnC,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAA;IAC9C,CAAC;AACH,CAAC;AARD,gDAQC;AAED,MAAa,UAAW,SAAQ,aAAM;IAGpC,YAAY,IAAY;QACtB,KAAK,CAAC,IAAI,CAAC,CAAA;QAHJ,YAAO,GAAG,IAAI,CAAA;IAIvB,CAAC;IAED,IAAI,MAAM;QACR,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;IAC/B,CAAC;IAED,2BAA2B;IAC3B,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,WAAW;IACb,CAAC;CACF;AAfD,gCAeC", "sourcesContent": ["import { addValue, Arch, archFromString, ArchType, asArray } from \"builder-util\"\nimport { DEFAULT_TARGET, DIR_TARGET, Platform, Target, TargetConfiguration } from \"../core\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { ArchiveTarget } from \"./ArchiveTarget\"\n\nconst archiveTargets = new Set([\"zip\", \"7z\", \"tar.xz\", \"tar.lz\", \"tar.gz\", \"tar.bz2\"])\n\nexport function computeArchToTargetNamesMap(raw: Map<Arch, Array<string>>, platformPackager: PlatformPackager<any>, platform: Platform): Map<Arch, Array<string>> {\n  for (const targetNames of raw.values()) {\n    if (targetNames.length > 0) {\n      // https://github.com/electron-userland/electron-builder/issues/1355\n      return raw\n    }\n  }\n\n  const defaultArchs: Array<ArchType> = raw.size === 0 ? [process.arch as ArchType] : Array.from(raw.keys()).map(it => Arch[it] as ArchType)\n  const result = new Map(raw)\n  for (const target of asArray(platformPackager.platformSpecificBuildOptions.target).map<TargetConfiguration>(it => (typeof it === \"string\" ? { target: it } : it))) {\n    let name = target.target\n    let archs = target.arch\n    const suffixPos = name.lastIndexOf(\":\")\n    if (suffixPos > 0) {\n      name = target.target.substring(0, suffixPos)\n      if (archs == null) {\n        archs = target.target.substring(suffixPos + 1) as ArchType\n      }\n    }\n\n    for (const arch of archs == null ? defaultArchs : asArray(archs)) {\n      addValue(result, archFromString(arch), name)\n    }\n  }\n\n  if (result.size === 0) {\n    const defaultTarget = platformPackager.defaultTarget\n    if (raw.size === 0 && platform === Platform.LINUX && (process.platform === \"darwin\" || process.platform === \"win32\")) {\n      result.set(Arch.x64, defaultTarget)\n      // cannot enable arm because of native dependencies - e.g. keytar doesn't provide pre-builds for arm\n      // result.set(Arch.armv7l, [\"snap\"])\n    } else {\n      for (const arch of defaultArchs) {\n        result.set(archFromString(arch), defaultTarget)\n      }\n    }\n  }\n\n  return result\n}\n\nexport function createTargets(nameToTarget: Map<string, Target>, rawList: Array<string>, outDir: string, packager: PlatformPackager<any>): Array<Target> {\n  const result: Array<Target> = []\n\n  const mapper = (name: string, factory: (outDir: string) => Target) => {\n    let target = nameToTarget.get(name)\n    if (target == null) {\n      target = factory(outDir)\n      nameToTarget.set(name, target)\n    }\n    result.push(target)\n  }\n\n  const targets = normalizeTargets(rawList, packager.defaultTarget)\n  packager.createTargets(targets, mapper)\n  return result\n}\n\nfunction normalizeTargets(targets: Array<string>, defaultTarget: Array<string>): Array<string> {\n  const list: Array<string> = []\n  for (const t of targets) {\n    const name = t.toLowerCase().trim()\n    if (name === DEFAULT_TARGET) {\n      list.push(...defaultTarget)\n    } else {\n      list.push(name)\n    }\n  }\n  return list\n}\n\nexport function createCommonTarget(target: string, outDir: string, packager: PlatformPackager<any>): Target {\n  if (archiveTargets.has(target)) {\n    return new ArchiveTarget(target, outDir, packager)\n  } else if (target === DIR_TARGET) {\n    return new NoOpTarget(DIR_TARGET)\n  } else {\n    throw new Error(`Unknown target: ${target}`)\n  }\n}\n\nexport class NoOpTarget extends Target {\n  readonly options = null\n\n  constructor(name: string) {\n    super(name)\n  }\n\n  get outDir(): string {\n    throw new Error(\"NoOpTarget\")\n  }\n\n  // eslint-disable-next-line\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    // no build\n  }\n}\n"]}