const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// Global error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  dialog.showErrorBox('Application Error', 'An unexpected error occurred. Please restart the application.');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

let bannerWindow;
let controlWindow;

function createWindows() {
  try {
    // Create the banner window
    bannerWindow = new BrowserWindow({
    width: 1920,
    height: 200,
    transparent: true,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Create the control panel window
  controlWindow = new BrowserWindow({
    width: 800,
    height: 600,
    title: 'Banner Pro Control Panel',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  bannerWindow.loadFile('banner.html');
  controlWindow.loadFile('control.html');

  // Position the banner at the bottom of the screen
  try {
    const { screen } = require('electron');
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    bannerWindow.setPosition(0, height - 200);
    bannerWindow.setSize(width, 200);
  } catch (error) {
    console.error('Error positioning banner window:', error);
    // Fallback positioning
    bannerWindow.setPosition(0, 800);
  }
  // When control window is closed, close the banner window and quit the app
  controlWindow.on('closed', () => {
    controlWindow = null;
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.close();
    }
    bannerWindow = null;
    if (!app.isQuiting) {
      app.isQuiting = true;
      app.quit();
    }
  });

  // If banner window is closed, close control window and quit the app
  bannerWindow.on('closed', () => {
    bannerWindow = null;
    if (controlWindow && !controlWindow.isDestroyed()) {
      controlWindow.close();
    }
    controlWindow = null;
    if (!app.isQuiting) {
      app.isQuiting = true;
      app.quit();
    }
  });

  } catch (error) {
    console.error('Error creating windows:', error);
    dialog.showErrorBox('Startup Error', 'Failed to create application windows. Please try restarting the application.');
    app.quit();
  }
}

app.whenReady().then(createWindows);

app.on('window-all-closed', () => {
  // Clean up references
  bannerWindow = null;
  controlWindow = null;

  if (process.platform !== 'darwin') {
    if (!app.isQuiting) {
      app.isQuiting = true;
      app.quit();
    }
  }
});

app.on('before-quit', () => {
  app.isQuiting = true;
});

// Handle app activation (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindows();
  }
});

// Handle settings updates
ipcMain.on('update-settings', (event, settings) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.webContents.send('apply-settings', settings);
    }
  } catch (error) {
    console.log('Error sending settings to banner window:', error.message);
  }
});

// Handle banner visibility toggle
ipcMain.on('toggle-banner', (event) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      if (bannerWindow.isVisible()) {
        bannerWindow.hide();
        event.reply('banner-visibility-changed', false);
      } else {
        bannerWindow.show();
        event.reply('banner-visibility-changed', true);
      }
    }
  } catch (error) {
    console.log('Error toggling banner visibility:', error.message);
  }
});

// Handle banner show/hide requests
ipcMain.on('show-banner', (event) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.show();
      event.reply('banner-visibility-changed', true);
    }
  } catch (error) {
    console.log('Error showing banner:', error.message);
  }
});

ipcMain.on('hide-banner', (event) => {
  try {
    if (bannerWindow && !bannerWindow.isDestroyed()) {
      bannerWindow.hide();
      event.reply('banner-visibility-changed', false);
    }
  } catch (error) {
    console.log('Error hiding banner:', error.message);
  }
});

