{"version": 3, "file": "nsisLicense.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/nsisLicense.ts"], "names": [], "mappings": ";;;AAAA,4CAAuC;AACvC,gDAAgF;AAChF,6BAA4B;AAI5B,yCAA6C;AAEtC,KAAK,UAAU,kBAAkB,CAAC,QAAqB,EAAE,OAAoB,EAAE,eAAoC,EAAE,SAAwB;IAClJ,MAAM,OAAO,GAAG,MAAM,IAAA,oCAA0B,EAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IAC3E,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,IAAI,WAA0B,CAAA;QAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,WAAW,GAAG;gBACZ,kDAAkD;gBAClD,sBAAsB;gBACtB,0CAA0C;gBAC1C,2BAA2B;gBAC3B,+DAA+D;gBAC/D,aAAa;gBAEb,kCAAkC,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,mBAAmB,CAAC,GAAG;aACtF,CAAA;QACH,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,CAAC,kCAAkC,OAAO,GAAG,CAAC,CAAA;QAC9D,CAAC;QAED,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,0CAA0C,OAAO,GAAG,CAAC,CAAC,CAAA;QAClG,CAAC;QACD,OAAM;IACR,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,IAAA,yBAAe,EAAC,QAAQ,CAAC,CAAA;IACpD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAM;IACR,CAAC;IAED,MAAM,WAAW,GAAkB,EAAE,CAAA;IACrC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAA;IAE3C,IAAI,WAAW,GAAkB,IAAI,CAAA;IACrC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;QAChC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC5C,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,WAAW,GAAG,IAAI,CAAC,IAAI,CAAA;QACzB,CAAC;QACD,WAAW,CAAC,IAAI,CAAC,gCAAgC,YAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;IAC3G,CAAC;IAED,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE,CAAC;QACjC,WAAW,CAAC,IAAI,CAAC,gCAAgC,YAAI,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,CAAA;IAC9E,CAAC;IAED,WAAW,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAA;IACjE,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;AACnD,CAAC;AAjDD,gDAiDC", "sourcesContent": ["import { lcid } from \"../../util/langs\"\nimport { getLicenseFiles, getNotLocalizedLicenseFile } from \"../../util/license\"\nimport * as path from \"path\"\nimport { WinPackager } from \"../../winPackager\"\nimport { NsisOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { nsisTemplatesDir } from \"./nsisUtil\"\n\nexport async function computeLicensePage(packager: WinPackager, options: NsisOptions, scriptGenerator: NsisScriptGenerator, languages: Array<string>): Promise<void> {\n  const license = await getNotLocalizedLicenseFile(options.license, packager)\n  if (license != null) {\n    let licensePage: Array<string>\n    if (license.endsWith(\".html\")) {\n      licensePage = [\n        \"!define MUI_PAGE_CUSTOMFUNCTION_SHOW LicenseShow\",\n        \"Function LicenseShow\",\n        \"  FindWindow $R0 `#32770` `` $HWNDPARENT\",\n        \"  GetDlgItem $R0 $R0 1000\",\n        \"EmbedHTML::Load /replace $R0 file://$PLUGINSDIR\\\\license.html\",\n        \"FunctionEnd\",\n\n        `!insertmacro MUI_PAGE_LICENSE \"${path.join(nsisTemplatesDir, \"empty-license.txt\")}\"`,\n      ]\n    } else {\n      licensePage = [`!insertmacro MUI_PAGE_LICENSE \"${license}\"`]\n    }\n\n    scriptGenerator.macro(\"licensePage\", licensePage)\n    if (license.endsWith(\".html\")) {\n      scriptGenerator.macro(\"addLicenseFiles\", [`File /oname=$PLUGINSDIR\\\\license.html \"${license}\"`])\n    }\n    return\n  }\n\n  const licenseFiles = await getLicenseFiles(packager)\n  if (licenseFiles.length === 0) {\n    return\n  }\n\n  const licensePage: Array<string> = []\n  const unspecifiedLangs = new Set(languages)\n\n  let defaultFile: string | null = null\n  for (const item of licenseFiles) {\n    unspecifiedLangs.delete(item.langWithRegion)\n    if (defaultFile == null) {\n      defaultFile = item.file\n    }\n    licensePage.push(`LicenseLangString MUILicense ${lcid[item.langWithRegion] || item.lang} \"${item.file}\"`)\n  }\n\n  for (const l of unspecifiedLangs) {\n    licensePage.push(`LicenseLangString MUILicense ${lcid[l]} \"${defaultFile}\"`)\n  }\n\n  licensePage.push('!insertmacro MUI_PAGE_LICENSE \"$(MUILicense)\"')\n  scriptGenerator.macro(\"licensePage\", licensePage)\n}\n"]}