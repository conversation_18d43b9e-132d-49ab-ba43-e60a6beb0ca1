#!/usr/bin/env node

/**
 * Quick test script to generate license keys
 * Run: node test-license-generation.js
 */

const LicenseSystem = require('./license-system');

console.log('🚀 Banner Pro License Key Generator Test\n');

// Create license system instance
const licenseSystem = new LicenseSystem();

// Generate 5 sample license keys
console.log('Generating 5 sample license keys:\n');

for (let i = 1; i <= 5; i++) {
  const customerInfo = {
    name: `Customer ${i}`,
    email: `customer${i}@example.com`,
    company: `Company ${i}`
  };

  const result = licenseSystem.generateLicenseKey(customerInfo);
  
  console.log(`License #${i}:`);
  console.log(`  Key: ${result.licenseKey}`);
  console.log(`  Customer: ${customerInfo.name}`);
  console.log(`  Email: ${customerInfo.email}`);
  console.log(`  Valid Format: ${licenseSystem.validateLicenseKeyFormat(result.licenseKey) ? '✅ Yes' : '❌ No'}`);
  console.log('');
}

console.log('✅ Test completed! These are real license keys you can use.');
console.log('💡 Copy any of these keys to test activation in the app.');

// Show hardware fingerprint for testing
console.log('\n🖥️  Current Hardware Fingerprint:');
console.log(`   ${licenseSystem.generateHardwareFingerprint()}`);
console.log('   (This is what the license will be bound to)');

// Show trial status
console.log('\n⏱️  Trial Status:');
const trialStatus = licenseSystem.getTrialStatus();
console.log(`   Trial Active: ${trialStatus.isTrialActive ? '✅ Yes' : '❌ No'}`);
console.log(`   Days Remaining: ${trialStatus.daysRemaining}`);
if (trialStatus.startDate) {
  console.log(`   Started: ${trialStatus.startDate}`);
}
