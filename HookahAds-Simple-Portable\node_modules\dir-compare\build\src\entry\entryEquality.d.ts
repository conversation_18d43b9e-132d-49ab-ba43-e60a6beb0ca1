export function isEntryEqualSync(entry1: any, entry2: any, type: any, options: any): {
    same: boolean;
    reason: string;
} | {
    same: boolean;
    reason?: undefined;
};
export function isEntryEqualSync(entry1: any, entry2: any, type: any, options: any): {
    same: boolean;
    reason: string;
} | {
    same: boolean;
    reason?: undefined;
};
export function isEntryEqualAsync(entry1: any, entry2: any, type: any, diffSet: any, options: any): {
    same: undefined;
    samePromise: any;
    reason?: undefined;
} | {
    same: boolean;
    reason: string;
} | {
    same: boolean;
    reason?: undefined;
};
export function isEntryEqualAsync(entry1: any, entry2: any, type: any, diffSet: any, options: any): {
    same: undefined;
    samePromise: any;
    reason?: undefined;
} | {
    same: boolean;
    reason: string;
} | {
    same: boolean;
    reason?: undefined;
};
//# sourceMappingURL=entryEquality.d.ts.map