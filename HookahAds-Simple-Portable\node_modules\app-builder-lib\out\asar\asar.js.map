{"version": 3, "file": "asar.js", "sourceRoot": "", "sources": ["../../src/asar/asar.ts"], "names": [], "mappings": ";;;AAAA,2DAAqD;AACrD,uCAA6D;AAC7D,6BAA4B;AAc5B,MAAa,IAAI;CAehB;AAfD,oBAeC;AAED,MAAa,cAAc;IAGzB,YACW,GAAW,EACX,SAAS,IAAI,IAAI,EAAE,EACnB,aAAqB,CAAC,CAAC;QAFvB,QAAG,GAAH,GAAG,CAAQ;QACX,WAAM,GAAN,MAAM,CAAa;QACnB,eAAU,GAAV,UAAU,CAAa;QAL1B,WAAM,GAAG,CAAC,CAAA;QAOhB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAA;QACxB,CAAC;IACH,CAAC;IAED,uBAAuB,CAAC,CAAS,EAAE,QAAiB;QAClD,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAA;QACtB,KAAK,MAAM,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACpC,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;gBAChB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAM,CAAC,GAAG,CAAC,CAAA;gBAC5B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;oBAClB,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACd,OAAO,IAAI,CAAA;oBACb,CAAC;oBACD,KAAK,GAAG,IAAI,IAAI,EAAE,CAAA;oBAClB,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;oBAChB,IAAI,CAAC,KAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;gBAC1B,CAAC;gBACD,IAAI,GAAG,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,eAAe,CAAC,CAAS;QACvB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,MAAM,CAAA;QACpB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAE,CAAA;QACpE,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,KAAK,GAAG,EAAE,CAAA;QACpB,CAAC;QAED,IAAI,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,GAAG,IAAI,IAAI,EAAE,CAAA;YACnB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAA;QAC9B,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,OAAa,EAAE,IAAY,EAAE,QAAiB,EAAE,IAAW,EAAE,SAAyB;QAC9G,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,yCAAyC,CAAC,CAAA;QACnE,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC5B,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACtB,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;YACpC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC;gBACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACxB,CAAC;YACD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAA;QAC1B,CAAC;QAED,IAAI,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAA;QAC5B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,QAAQ,GAAG,EAAE,CAAA;YACb,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAA;QAC1B,CAAC;QACD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;QAEpC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO,CAAC,CAAS;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAE,CAAA;QAClE,OAAO,IAAI,CAAC,KAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACtC,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,WAAW,GAAG,IAAI;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE,CAAA;QAC7B,oDAAoD;QACpD,OAAO,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC1E,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;IAC3D,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,OAAO,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;IACzD,CAAC;CACF;AApGD,wCAoGC;AAEM,KAAK,UAAU,cAAc,CAAC,OAAe;IAClD,MAAM,EAAE,GAAG,MAAM,IAAA,eAAI,EAAC,OAAO,EAAE,GAAG,CAAC,CAAA;IACnC,IAAI,IAAY,CAAA;IAChB,IAAI,SAAS,CAAA;IACb,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QACrC,IAAI,CAAC,MAAM,IAAA,eAAI,EAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,IAAW,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QAED,MAAM,UAAU,GAAG,IAAA,qCAAgB,EAAC,OAAO,CAAC,CAAA;QAC5C,IAAI,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC,UAAU,EAAE,CAAA;QAC/C,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,IAAA,eAAI,EAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAW,CAAC,CAAC,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;QAC1C,CAAC;IACH,CAAC;YAAS,CAAC;QACT,MAAM,IAAA,gBAAK,EAAC,EAAE,CAAC,CAAA;IACjB,CAAC;IAED,MAAM,YAAY,GAAG,IAAA,qCAAgB,EAAC,SAAS,CAAC,CAAA;IAChD,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,cAAc,EAAE,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAA;AACrE,CAAC;AAtBD,wCAsBC;AAEM,KAAK,UAAU,QAAQ,CAAC,OAAe;IAC5C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,CAAA;IACtD,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAA;AAC9D,CAAC;AAHD,4BAGC;AAEM,KAAK,UAAU,YAAY,CAAC,OAAe,EAAE,IAAY;IAC9D,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA;IAClC,OAAO,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;AAChC,CAAC;AAHD,oCAGC;AAED,KAAK,UAAU,gBAAgB,CAAC,UAA0B,EAAE,QAAgB,EAAE,IAAU;IACtF,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAA;IACvB,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IACvC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;QACd,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,OAAO,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,GAAG,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED,MAAM,EAAE,GAAG,MAAM,IAAA,eAAI,EAAC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC1C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAO,EAAE,EAAE,CAAC,CAAA;QACrE,MAAM,IAAA,eAAI,EAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IACzC,CAAC;YAAS,CAAC;QACT,MAAM,IAAA,gBAAK,EAAC,EAAE,CAAC,CAAA;IACjB,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import { create<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"chromium-pickle-js\"\nimport { close, open, read, readFile, Stats } from \"fs-extra\"\nimport * as path from \"path\"\n\nexport interface ReadAsarHeader {\n  readonly header: string\n  readonly size: number\n}\n\nexport interface NodeIntegrity {\n  algorithm: \"SHA256\"\n  hash: string\n  blockSize: number\n  blocks: Array<string>\n}\n\nexport class Node {\n  // we don't use Map because later it will be stringified\n  files?: { [key: string]: Node }\n\n  unpacked?: boolean\n\n  size?: number\n  // electron expects string\n  offset?: string\n\n  executable?: boolean\n\n  link?: string\n\n  integrity?: NodeIntegrity\n}\n\nexport class AsarFilesystem {\n  private offset = 0\n\n  constructor(\n    readonly src: string,\n    readonly header = new Node(),\n    readonly headerSize: number = -1\n  ) {\n    if (this.header.files == null) {\n      this.header.files = {}\n    }\n  }\n\n  searchNodeFromDirectory(p: string, isCreate: boolean): Node | null {\n    let node = this.header\n    for (const dir of p.split(path.sep)) {\n      if (dir !== \".\") {\n        let child = node.files![dir]\n        if (child == null) {\n          if (!isCreate) {\n            return null\n          }\n          child = new Node()\n          child.files = {}\n          node.files![dir] = child\n        }\n        node = child\n      }\n    }\n    return node\n  }\n\n  getOrCreateNode(p: string): Node {\n    if (p == null || p.length === 0) {\n      return this.header\n    }\n\n    const name = path.basename(p)\n    const dirNode = this.searchNodeFromDirectory(path.dirname(p), true)!\n    if (dirNode.files == null) {\n      dirNode.files = {}\n    }\n\n    let result = dirNode.files[name]\n    if (result == null) {\n      result = new Node()\n      dirNode.files[name] = result\n    }\n    return result\n  }\n\n  addFileNode(file: string, dirNode: Node, size: number, unpacked: boolean, stat: Stats, integrity?: NodeIntegrity): Node {\n    if (size > 4294967295) {\n      throw new Error(`${file}: file size cannot be larger than 4.2GB`)\n    }\n\n    const node = new Node()\n    node.size = size\n    if (integrity) {\n      node.integrity = integrity\n    }\n    if (unpacked) {\n      node.unpacked = true\n    } else {\n      // electron expects string\n      node.offset = this.offset.toString()\n      if (process.platform !== \"win32\" && stat.mode & 0o100) {\n        node.executable = true\n      }\n      this.offset += node.size\n    }\n\n    let children = dirNode.files\n    if (children == null) {\n      children = {}\n      dirNode.files = children\n    }\n    children[path.basename(file)] = node\n\n    return node\n  }\n\n  getNode(p: string): Node | null {\n    const node = this.searchNodeFromDirectory(path.dirname(p), false)!\n    return node.files![path.basename(p)]\n  }\n\n  getFile(p: string, followLinks = true): Node {\n    const info = this.getNode(p)!\n    // if followLinks is false we don't resolve symlinks\n    return followLinks && info.link != null ? this.getFile(info.link) : info\n  }\n\n  async readJson(file: string): Promise<any> {\n    return JSON.parse((await this.readFile(file)).toString())\n  }\n\n  readFile(file: string): Promise<Buffer> {\n    return readFileFromAsar(this, file, this.getFile(file))\n  }\n}\n\nexport async function readAsarHeader(archive: string): Promise<ReadAsarHeader> {\n  const fd = await open(archive, \"r\")\n  let size: number\n  let headerBuf\n  try {\n    const sizeBuf = Buffer.allocUnsafe(8)\n    if ((await read(fd, sizeBuf, 0, 8, null as any)).bytesRead !== 8) {\n      throw new Error(\"Unable to read header size\")\n    }\n\n    const sizePickle = createFromBuffer(sizeBuf)\n    size = sizePickle.createIterator().readUInt32()\n    headerBuf = Buffer.allocUnsafe(size)\n    if ((await read(fd, headerBuf, 0, size, null as any)).bytesRead !== size) {\n      throw new Error(\"Unable to read header\")\n    }\n  } finally {\n    await close(fd)\n  }\n\n  const headerPickle = createFromBuffer(headerBuf)\n  return { header: headerPickle.createIterator().readString(), size }\n}\n\nexport async function readAsar(archive: string): Promise<AsarFilesystem> {\n  const { header, size } = await readAsarHeader(archive)\n  return new AsarFilesystem(archive, JSON.parse(header), size)\n}\n\nexport async function readAsarJson(archive: string, file: string): Promise<any> {\n  const fs = await readAsar(archive)\n  return await fs.readJson(file)\n}\n\nasync function readFileFromAsar(filesystem: AsarFilesystem, filename: string, info: Node): Promise<Buffer> {\n  const size = info.size!\n  const buffer = Buffer.allocUnsafe(size)\n  if (size <= 0) {\n    return buffer\n  }\n\n  if (info.unpacked) {\n    return await readFile(path.join(`${filesystem.src}.unpacked`, filename))\n  }\n\n  const fd = await open(filesystem.src, \"r\")\n  try {\n    const offset = 8 + filesystem.headerSize + parseInt(info.offset!, 10)\n    await read(fd, buffer, 0, size, offset)\n  } finally {\n    await close(fd)\n  }\n  return buffer\n}\n"]}