#!/usr/bin/env node

/**
 * Test script to check current license status
 * Run: node test-license-status.js
 */

const LicenseSystem = require('./license-system');

console.log('🔍 Banner Pro License Status Check\n');

try {
  const licenseSystem = new LicenseSystem();

  // Check if license is activated
  const isActivated = licenseSystem.isLicenseActivated();
  console.log(`License Activated: ${isActivated ? '✅ Yes' : '❌ No'}`);

  if (isActivated) {
    const licenseInfo = licenseSystem.getLicenseInfo();
    console.log('\n📄 License Information:');
    console.log(`  License Key: ${licenseInfo.licenseKey}`);
    console.log(`  Activated: ${licenseInfo.activatedAt}`);
    console.log(`  Customer: ${licenseInfo.customerInfo?.name || 'N/A'}`);
    console.log(`  Hardware: ${licenseInfo.hardwareFingerprint}`);
  }

  // Check trial status
  const trialStatus = licenseSystem.getTrialStatus();
  console.log('\n⏱️  Trial Status:');
  console.log(`  Trial Active: ${trialStatus.isTrialActive ? '✅ Yes' : '❌ No'}`);
  console.log(`  Days Remaining: ${trialStatus.daysRemaining}`);
  if (trialStatus.startDate) {
    console.log(`  Started: ${trialStatus.startDate}`);
  }

  // Show current hardware fingerprint
  console.log('\n🖥️  Hardware Fingerprint:');
  console.log(`  ${licenseSystem.generateHardwareFingerprint()}`);

  // Determine what should happen on startup
  console.log('\n🚀 Startup Behavior:');
  if (isActivated) {
    console.log('  ✅ Should start main application (licensed)');
  } else if (trialStatus.isTrialActive) {
    console.log('  ✅ Should start main application (trial active)');
  } else {
    console.log('  🔑 Should show license activation window');
  }

} catch (error) {
  console.error('❌ Error checking license status:', error.message);
  console.log('\n🔑 Should show license activation window (fallback)');
}

console.log('\n💡 If the app is not showing the activation window when it should,');
console.log('   there might be an issue with the license check logic.');
