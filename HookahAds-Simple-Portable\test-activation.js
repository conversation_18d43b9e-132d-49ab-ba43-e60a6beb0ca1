// Test script to force activation window
const { app, BrowserWindow } = require('electron');
const path = require('path');

let licenseWindow;

function createLicenseWindow() {
  licenseWindow = new BrowserWindow({
    width: 600,
    height: 700,
    title: 'Banner Pro - License Activation (TEST MODE)',
    resizable: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  licenseWindow.loadFile('license-activation.html');

  licenseWindow.on('closed', () => {
    licenseWindow = null;
    app.quit();
  });
}

app.whenReady().then(() => {
  createLicenseWindow();
});

app.on('window-all-closed', () => {
  app.quit();
});

console.log('🧪 Test mode: Showing activation window directly');
